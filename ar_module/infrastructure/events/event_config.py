# coding=utf-8
"""
Event Configuration for AR Module Event Framework

This module provides configuration settings for the AR event framework,
including exchange names, routing keys, and queue configurations.
"""
from typing import Dict, List
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.credentials.aws_secret_manager import AwsSecretManager


class AREventConfig:
    """Configuration for AR Event Framework"""
    
    # Exchange configuration
    EXCHANGE_NAME = "ar-integration-events"
    EXCHANGE_TYPE = "topic"
    EXCHANGE_DURABLE = True
    
    # Queue configuration
    QUEUE_DURABLE = True
    QUEUE_AUTO_DELETE = False
    QUEUE_EXCLUSIVE = False
    
    # Message configuration
    MESSAGE_DELIVERY_MODE = 2  # Persistent messages
    MESSAGE_TTL = ********  # 24 hours in milliseconds
    
    # Routing key patterns
    ROUTING_KEY_PATTERNS = {
        "credit_created": "ar.events.credit.create",
        "credit_cancelled": "ar.events.credit.cancel",
        "credit_reversed": "ar.events.credit.reverse",
        "credit_reversal_cancelled": "ar.events.credit_reversal.cancel",
        "debit_created": "ar.events.debit.create",
        "debit_updated": "ar.events.debit.update",
        "debit_cancelled": "ar.events.debit.cancel",
        "settlement_created": "ar.events.settlement.create",
        "settlement_updated": "ar.events.settlement.update",
    }
    
    # Consumer routing keys
    CONSUMER_ROUTING_KEYS = [
        "ar.events.#",  # All AR events
    ]
    
    # Publisher routing keys
    PUBLISHER_ROUTING_KEYS = [
        "ar.events.credit.#",     # All credit events
        "ar.events.debit.#",      # All debit events
        "ar.events.settlement.#", # All settlement events
    ]


class AREventPublisherConfig:
    """Configuration for AR Event Publisher"""
    
    def __init__(self, tenant_id: str = None):
        self.tenant_id = tenant_id or TenantClient.get_default_tenant()
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(self.tenant_id)
        self.exchange_name = AREventConfig.EXCHANGE_NAME
        self.exchange_type = AREventConfig.EXCHANGE_TYPE
        self.exchange_durable = AREventConfig.EXCHANGE_DURABLE
        self.message_delivery_mode = AREventConfig.MESSAGE_DELIVERY_MODE
        self.message_ttl = AREventConfig.MESSAGE_TTL
        self.routing_keys = AREventConfig.PUBLISHER_ROUTING_KEYS


class AREventConsumerConfig:
    """Configuration for AR Event Consumer"""
    
    def __init__(self, tenant_id: str = None, queue_suffix: str = ""):
        self.tenant_id = tenant_id or TenantClient.get_default_tenant()
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(self.tenant_id)
        self.exchange_name = AREventConfig.EXCHANGE_NAME
        self.exchange_type = AREventConfig.EXCHANGE_TYPE
        self.exchange_durable = AREventConfig.EXCHANGE_DURABLE
        
        # Queue configuration
        base_queue_name = "ar-integration-events-consumer"
        self.queue_name = f"{base_queue_name}-{queue_suffix}" if queue_suffix else base_queue_name
        self.queue_durable = AREventConfig.QUEUE_DURABLE
        self.queue_auto_delete = AREventConfig.QUEUE_AUTO_DELETE
        self.queue_exclusive = AREventConfig.QUEUE_EXCLUSIVE
        
        # Routing keys
        self.routing_keys = AREventConfig.CONSUMER_ROUTING_KEYS


class AREventRoutingConfig:
    """Configuration for event routing"""
    
    @staticmethod
    def get_routing_key(entity_type: str, action: str) -> str:
        """
        Generate routing key for an event
        
        Args:
            entity_type: Type of entity (credit, debit, etc.)
            action: Action performed (create, update, etc.)
            
        Returns:
            str: Routing key
        """
        return f"ar.events.{entity_type}.{action}"
    
    @staticmethod
    def get_routing_keys_for_entity(entity_type: str) -> List[str]:
        """
        Get all routing keys for a specific entity type
        
        Args:
            entity_type: Type of entity
            
        Returns:
            List[str]: List of routing keys
        """
        return [f"ar.events.{entity_type}.#"]
    
    @staticmethod
    def get_routing_keys_for_action(action: str) -> List[str]:
        """
        Get all routing keys for a specific action
        
        Args:
            action: Action type
            
        Returns:
            List[str]: List of routing keys
        """
        return [f"ar.events.*.{action}"]


class AREventEnvironmentConfig:
    """Environment-specific configuration for AR events"""
    
    def __init__(self, environment: str = "production"):
        self.environment = environment
        self.config = self._get_environment_config()
    
    def _get_environment_config(self) -> Dict:
        """Get configuration based on environment"""
        configs = {
            "development": {
                "exchange_name": "ar-integration-events-dev",
                "queue_prefix": "dev-",
                "message_ttl": 3600000,  # 1 hour
                "enable_debug_logging": True,
                "enable_event_validation": True,
            },
            "staging": {
                "exchange_name": "ar-integration-events-staging",
                "queue_prefix": "staging-",
                "message_ttl": ********,  # 12 hours
                "enable_debug_logging": True,
                "enable_event_validation": True,
            },
            "production": {
                "exchange_name": "ar-integration-events",
                "queue_prefix": "",
                "message_ttl": ********,  # 24 hours
                "enable_debug_logging": False,
                "enable_event_validation": False,
            }
        }
        
        return configs.get(self.environment, configs["production"])
    
    def get_exchange_name(self) -> str:
        """Get environment-specific exchange name"""
        return self.config["exchange_name"]
    
    def get_queue_prefix(self) -> str:
        """Get environment-specific queue prefix"""
        return self.config["queue_prefix"]
    
    def get_message_ttl(self) -> int:
        """Get environment-specific message TTL"""
        return self.config["message_ttl"]
    
    def is_debug_logging_enabled(self) -> bool:
        """Check if debug logging is enabled"""
        return self.config["enable_debug_logging"]
    
    def is_event_validation_enabled(self) -> bool:
        """Check if event validation is enabled"""
        return self.config["enable_event_validation"]


# Default configurations
DEFAULT_PUBLISHER_CONFIG = AREventPublisherConfig()
DEFAULT_CONSUMER_CONFIG = AREventConsumerConfig()
DEFAULT_ROUTING_CONFIG = AREventRoutingConfig()

# Environment-specific configurations
DEV_ENV_CONFIG = AREventEnvironmentConfig("development")
STAGING_ENV_CONFIG = AREventEnvironmentConfig("staging")
PROD_ENV_CONFIG = AREventEnvironmentConfig("production")


def get_event_config_for_environment(environment: str = "production") -> AREventEnvironmentConfig:
    """
    Get event configuration for a specific environment
    
    Args:
        environment: Environment name
        
    Returns:
        AREventEnvironmentConfig: Environment-specific configuration
    """
    return AREventEnvironmentConfig(environment)


def get_publisher_config(tenant_id: str = None) -> AREventPublisherConfig:
    """
    Get publisher configuration for a tenant
    
    Args:
        tenant_id: Tenant ID
        
    Returns:
        AREventPublisherConfig: Publisher configuration
    """
    return AREventPublisherConfig(tenant_id)


def get_consumer_config(tenant_id: str = None, queue_suffix: str = "") -> AREventConsumerConfig:
    """
    Get consumer configuration for a tenant
    
    Args:
        tenant_id: Tenant ID
        queue_suffix: Optional queue suffix
        
    Returns:
        AREventConsumerConfig: Consumer configuration
    """
    return AREventConsumerConfig(tenant_id, queue_suffix)
