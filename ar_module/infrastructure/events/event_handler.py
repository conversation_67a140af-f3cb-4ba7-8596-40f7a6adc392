# coding=utf-8
"""
Event Handler Infrastructure for AR Module Event Framework

This module provides base classes and utilities for handling AR events.
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, List, Type, Callable, Any

from ar_module.domain.events.base_event import BaseEvent

logger = logging.getLogger(__name__)


class EventHandler(ABC):
    """Abstract base class for event handlers"""
    
    @abstractmethod
    def handle(self, event: BaseEvent) -> bool:
        """
        Handle an event
        
        Args:
            event: The event to handle
            
        Returns:
            bool: True if handled successfully, False otherwise
        """
        pass
    
    @abstractmethod
    def can_handle(self, event: BaseEvent) -> bool:
        """
        Check if this handler can handle the given event
        
        Args:
            event: The event to check
            
        Returns:
            bool: True if this handler can handle the event
        """
        pass


class TypedEventHandler(EventHandler):
    """Event handler that handles specific event types"""
    
    def __init__(self, event_types: List[str]):
        self.event_types = set(event_types)
    
    def can_handle(self, event: BaseEvent) -> bool:
        """Check if this handler can handle the event type"""
        return event.type in self.event_types
    
    @abstractmethod
    def handle(self, event: BaseEvent) -> bool:
        """Handle the event - to be implemented by subclasses"""
        pass


class FunctionEventHandler(EventHandler):
    """Event handler that wraps a function"""
    
    def __init__(self, handler_func: Callable[[BaseEvent], bool], event_types: List[str] = None):
        self.handler_func = handler_func
        self.event_types = set(event_types) if event_types else set()
    
    def can_handle(self, event: BaseEvent) -> bool:
        """Check if this handler can handle the event"""
        if not self.event_types:
            return True  # Handle all events if no specific types specified
        return event.type in self.event_types
    
    def handle(self, event: BaseEvent) -> bool:
        """Handle the event using the wrapped function"""
        try:
            return self.handler_func(event)
        except Exception as e:
            logger.error(f"Error in function event handler: {e}")
            return False


class EventHandlerRegistry:
    """Registry for managing event handlers"""
    
    def __init__(self):
        self._handlers: List[EventHandler] = []
        self._type_handlers: Dict[str, List[EventHandler]] = {}
    
    def register_handler(self, handler: EventHandler):
        """Register an event handler"""
        self._handlers.append(handler)
        logger.info(f"Registered event handler: {handler.__class__.__name__}")
    
    def register_function_handler(
        self,
        handler_func: Callable[[BaseEvent], bool],
        event_types: List[str] = None
    ):
        """Register a function as an event handler"""
        handler = FunctionEventHandler(handler_func, event_types)
        self.register_handler(handler)
    
    def register_typed_handler(self, event_type: str, handler: EventHandler):
        """Register a handler for a specific event type"""
        if event_type not in self._type_handlers:
            self._type_handlers[event_type] = []
        self._type_handlers[event_type].append(handler)
        logger.info(f"Registered typed handler for {event_type}: {handler.__class__.__name__}")
    
    def get_handlers_for_event(self, event: BaseEvent) -> List[EventHandler]:
        """Get all handlers that can handle the given event"""
        handlers = []
        
        # Get handlers registered for specific event type
        if event.type in self._type_handlers:
            handlers.extend(self._type_handlers[event.type])
        
        # Get general handlers that can handle this event
        for handler in self._handlers:
            if handler.can_handle(event):
                handlers.append(handler)
        
        return handlers
    
    def handle_event(self, event: BaseEvent) -> bool:
        """
        Handle an event using all registered handlers
        
        Args:
            event: The event to handle
            
        Returns:
            bool: True if at least one handler processed the event successfully
        """
        handlers = self.get_handlers_for_event(event)
        
        if not handlers:
            logger.warning(f"No handlers found for event type: {event.type}")
            return False
        
        success_count = 0
        for handler in handlers:
            try:
                if handler.handle(event):
                    success_count += 1
                    logger.debug(f"Handler {handler.__class__.__name__} successfully processed event {event.id}")
                else:
                    logger.warning(f"Handler {handler.__class__.__name__} failed to process event {event.id}")
            except Exception as e:
                logger.error(f"Error in handler {handler.__class__.__name__} for event {event.id}: {e}")
        
        if success_count > 0:
            logger.info(f"Event {event.id} processed by {success_count}/{len(handlers)} handlers")
            return True
        else:
            logger.error(f"Event {event.id} failed to be processed by any handler")
            return False
    
    def clear_handlers(self):
        """Clear all registered handlers (useful for testing)"""
        self._handlers.clear()
        self._type_handlers.clear()


# Global event handler registry
_event_handler_registry: EventHandlerRegistry = EventHandlerRegistry()


def get_event_handler_registry() -> EventHandlerRegistry:
    """Get the global event handler registry"""
    return _event_handler_registry


def register_event_handler(handler: EventHandler):
    """Register an event handler with the global registry"""
    _event_handler_registry.register_handler(handler)


def register_function_handler(
    handler_func: Callable[[BaseEvent], bool],
    event_types: List[str] = None
):
    """Register a function as an event handler with the global registry"""
    _event_handler_registry.register_function_handler(handler_func, event_types)


def handle_event(event: BaseEvent) -> bool:
    """Handle an event using the global registry"""
    return _event_handler_registry.handle_event(event)


# Decorator for registering event handlers
def event_handler(event_types: List[str] = None):
    """
    Decorator for registering function-based event handlers
    
    Usage:
        @event_handler(['com.treebo.ar.credit.created'])
        def handle_credit_created(event: BaseEvent) -> bool:
            # Handle the event
            return True
    """
    def decorator(func: Callable[[BaseEvent], bool]):
        register_function_handler(func, event_types)
        return func
    return decorator


# Example event handlers for demonstration
class LoggingEventHandler(EventHandler):
    """Example handler that logs all events"""
    
    def can_handle(self, event: BaseEvent) -> bool:
        return True  # Handle all events
    
    def handle(self, event: BaseEvent) -> bool:
        logger.info(f"Logging event: {event.id} of type {event.type} for entity {event.entity_id}")
        return True


class AuditEventHandler(TypedEventHandler):
    """Example handler for audit events"""
    
    def __init__(self):
        super().__init__([
            "com.treebo.ar.credit.created",
            "com.treebo.ar.credit.reversed",
            "com.treebo.ar.debit.created"
        ])
    
    def handle(self, event: BaseEvent) -> bool:
        logger.info(f"Auditing event: {event.id} - {event.type}")
        # Here you would implement actual audit logic
        # For example, save to audit log, send to external system, etc.
        return True
