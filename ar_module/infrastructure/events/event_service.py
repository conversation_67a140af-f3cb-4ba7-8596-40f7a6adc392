# coding=utf-8
"""
Event Service for AR Module Event Framework

This module provides a high-level service for managing events in the AR module.
It coordinates event publishing and handling.
"""
import logging
from typing import List, Optional, Dict, Any

from ar_module.domain.events.base_event import BaseEvent, EntityType, EventSource, UserAction
from ar_module.domain.events.credit_events import (
    create_credit_created_event,
    create_credit_reversal_event,
    create_credit_reversal_cancelled_event,
    create_credit_cancelled_event
)
from ar_module.domain.events.debit_events import (
    create_debit_created_event,
    create_debit_updated_event,
    create_debit_cancelled_event
)
from .event_publisher import get_event_publisher, EventPublisher
from .event_handler import get_event_handler_registry, EventHandlerRegistry
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class AREventService:
    """
    High-level service for managing AR events
    
    This service provides convenient methods for emitting events
    and coordinates between event publishing and handling.
    """
    
    def __init__(
        self,
        event_publisher: Optional[EventPublisher] = None,
        event_handler_registry: Optional[EventHandlerRegistry] = None
    ):
        self.event_publisher = event_publisher or get_event_publisher()
        self.event_handler_registry = event_handler_registry or get_event_handler_registry()

    def enable_events(self):
        """Enable event processing"""
        self._enabled = True
        logger.info("AR Event Service enabled")
    
    def disable_events(self):
        """Disable event processing (useful for testing)"""
        self._enabled = False
        logger.info("AR Event Service disabled")
    
    def is_enabled(self) -> bool:
        """Check if event processing is enabled"""
        return self._enabled
    
    def emit_event(self, event: BaseEvent) -> bool:
        """
        Emit a single event
        
        Args:
            event: The event to emit
            
        Returns:
            bool: True if event was processed successfully
        """
        if not self._enabled:
            logger.debug(f"Event service disabled, skipping event: {event.id}")
            return True
        
        try:
            # Publish the event
            if not self.event_publisher.publish(event):
                logger.error(f"Failed to publish event: {event.id}")
                return False
            
            # Handle the event locally if there are handlers
            self.event_handler_registry.handle_event(event)
            
            return True
            
        except Exception as e:
            logger.error(f"Error emitting event {event.id}: {e}")
            return False
    
    def emit_events(self, events: List[BaseEvent]) -> bool:
        """
        Emit multiple events
        
        Args:
            events: List of events to emit
            
        Returns:
            bool: True if all events were processed successfully
        """
        if not self._enabled:
            logger.debug(f"Event service disabled, skipping {len(events)} events")
            return True
        
        success_count = 0
        for event in events:
            if self.emit_event(event):
                success_count += 1
        
        total_events = len(events)
        if success_count == total_events:
            logger.info(f"Successfully emitted all {total_events} events")
            return True
        else:
            logger.warning(f"Emitted {success_count}/{total_events} events")
            return False
    
    # Convenience methods for emitting specific AR events
    
    def emit_credit_created(
        self,
        credit_id: str,
        recorded_via: EventSource,
        credit_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """Emit a credit created event"""
        event = create_credit_created_event(
            credit_id=credit_id,
            recorded_via=recorded_via,
            credit_data=credit_data,
            **kwargs
        )
        return self.emit_event(event)
    
    def emit_credit_reversal(
        self,
        credit_reversal_id: str,
        credit_id: str,
        recorded_via: EventSource,
        reversal_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """Emit a credit reversal event"""
        event = create_credit_reversal_event(
            credit_reversal_id=credit_reversal_id,
            credit_id=credit_id,
            recorded_via=recorded_via,
            reversal_data=reversal_data,
            **kwargs
        )
        return self.emit_event(event)
    
    def emit_credit_reversal_cancelled(
        self,
        credit_reversal_id: str,
        credit_id: str,
        recorded_via: EventSource,
        cancellation_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """Emit a credit reversal cancelled event"""
        event = create_credit_reversal_cancelled_event(
            credit_reversal_id=credit_reversal_id,
            credit_id=credit_id,
            recorded_via=recorded_via,
            cancellation_data=cancellation_data,
            **kwargs
        )
        return self.emit_event(event)
    
    def emit_credit_cancelled(
        self,
        credit_id: str,
        recorded_via: EventSource,
        cancellation_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """Emit a credit cancelled event"""
        event = create_credit_cancelled_event(
            credit_id=credit_id,
            recorded_via=recorded_via,
            cancellation_data=cancellation_data,
            **kwargs
        )
        return self.emit_event(event)
    
    def emit_debit_created(
        self,
        debit_id: str,
        recorded_via: EventSource,
        debit_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """Emit a debit created event"""
        event = create_debit_created_event(
            debit_id=debit_id,
            recorded_via=recorded_via,
            debit_data=debit_data,
            **kwargs
        )
        return self.emit_event(event)
    
    def emit_debit_updated(
        self,
        debit_id: str,
        recorded_via: EventSource,
        update_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """Emit a debit updated event"""
        event = create_debit_updated_event(
            debit_id=debit_id,
            recorded_via=recorded_via,
            update_data=update_data,
            **kwargs
        )
        return self.emit_event(event)
    
    def emit_debit_cancelled(
        self,
        debit_id: str,
        recorded_via: EventSource,
        cancellation_data: Dict[str, Any],
        **kwargs
    ) -> bool:
        """Emit a debit cancelled event"""
        event = create_debit_cancelled_event(
            debit_id=debit_id,
            recorded_via=recorded_via,
            cancellation_data=cancellation_data,
            **kwargs
        )
        return self.emit_event(event)


# Global event service instance
_event_service: Optional[AREventService] = None


def get_event_service() -> AREventService:
    """Get the global event service instance"""
    global _event_service
    if _event_service is None:
        _event_service = AREventService()
    return _event_service


def set_event_service(service: AREventService):
    """Set the global event service instance (useful for testing)"""
    global _event_service
    _event_service = service


# Convenience functions for emitting events
def emit_credit_created_event(
    credit_id: str,
    recorded_via: EventSource,
    credit_data: Dict[str, Any],
    **kwargs
) -> bool:
    """Convenience function to emit a credit created event"""
    return get_event_service().emit_credit_created(credit_id, recorded_via, credit_data, **kwargs)


def emit_credit_reversal_event(
    credit_reversal_id: str,
    credit_id: str,
    recorded_via: EventSource,
    reversal_data: Dict[str, Any],
    **kwargs
) -> bool:
    """Convenience function to emit a credit reversal event"""
    return get_event_service().emit_credit_reversal(
        credit_reversal_id, credit_id, recorded_via, reversal_data, **kwargs
    )


def emit_debit_created_event(
    debit_id: str,
    recorded_via: EventSource,
    debit_data: Dict[str, Any],
    **kwargs
) -> bool:
    """Convenience function to emit a debit created event"""
    return get_event_service().emit_debit_created(debit_id, recorded_via, debit_data, **kwargs)
