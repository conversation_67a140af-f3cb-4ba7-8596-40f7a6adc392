# coding=utf-8
"""
Event Publisher for AR Module Event Framework

This module provides the event publishing infrastructure that integrates
with the existing RabbitMQ messaging system.
"""
import logging
from abc import ABC, abstractmethod
from typing import List, Optional

from kombu import Exchange, Producer
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from ar_module.domain.events.base_event import BaseEvent, validate_event
from ar_module.infrastructure.messaging.queue_service import BaseQueueService
from ar_module.infrastructure.events.event_config import AREventPublisherConfig, AREventRoutingConfig
from object_registry import register_instance

logger = logging.getLogger(__name__)


class EventPublisher(ABC):
    """Abstract base class for event publishers"""
    
    @abstractmethod
    def publish(self, event: BaseEvent) -> bool:
        """Publish an event"""
        pass
    
    @abstractmethod
    def publish_batch(self, events: List[BaseEvent]) -> bool:
        """Publish multiple events in a batch"""
        pass


# Remove the old AREventPublisherConfig class as it's now in event_config.py


@register_instance()
class RabbitMQEventPublisher(BaseQueueService, EventPublisher):
    """
    RabbitMQ-based event publisher for AR events
    
    This publisher integrates with the existing AR messaging infrastructure
    and publishes events to a dedicated exchange for integration purposes.
    """
    
    def __init__(self):
        super().__init__()
        self._config = None
        self._tenant_wise_producers = {}
    
    def _setup_entities(self):
        """Setup RabbitMQ exchange and producers for each tenant"""
        self._config = AREventPublisherConfig()
        exchange = Exchange(
            self._config.exchange_name,
            type=self._config.exchange_type,
            durable=self._config.exchange_durable
        )
        
        for tenant_id, conn in self.tenant_wise_connection.items():
            try:
                self._tenant_wise_producers[tenant_id] = Producer(
                    channel=conn.channel(),
                    exchange=exchange
                )
                logger.info(f"Event publisher setup completed for tenant: {tenant_id}")
            except Exception as e:
                logger.error(f"Failed to setup event publisher for tenant {tenant_id}: {e}")
    
    def publish(self, event: BaseEvent) -> bool:
        """
        Publish a single event
        
        Args:
            event: The event to publish
            
        Returns:
            bool: True if published successfully, False otherwise
        """
        if not validate_event(event):
            logger.error(f"Invalid event: {event}")
            return False
        
        try:
            self._initialize()
            tenant_id = event.tenant_id or get_current_tenant_id() or TenantClient.get_default_tenant()
            
            if tenant_id not in self._tenant_wise_producers:
                logger.error(f"No producer configured for tenant: {tenant_id}")
                return False
            
            producer = self._tenant_wise_producers[tenant_id]
            routing_key = self._get_routing_key(event)
            payload = event.to_dict()
            
            logger.debug(f"Publishing event: {event.id} with routing key: {routing_key}")
            
            self._publish(producer, payload, routing_key)
            
            logger.info(f"Successfully published event: {event.id} of type: {event.type}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to publish event {event.id}: {e}")
            return False
    
    def publish_batch(self, events: List[BaseEvent]) -> bool:
        """
        Publish multiple events in a batch
        
        Args:
            events: List of events to publish
            
        Returns:
            bool: True if all events published successfully, False otherwise
        """
        if not events:
            return True
        
        success_count = 0
        for event in events:
            if self.publish(event):
                success_count += 1
        
        total_events = len(events)
        if success_count == total_events:
            logger.info(f"Successfully published all {total_events} events")
            return True
        else:
            logger.warning(f"Published {success_count}/{total_events} events")
            return False
    
    def _get_routing_key(self, event: BaseEvent) -> str:
        """
        Generate routing key for the event using the routing configuration

        Format: ar.events.{entity_type}.{user_action}
        Example: ar.events.credit.create
        """
        return AREventRoutingConfig.get_routing_key(
            event.entity_type.value,
            event.user_action.value
        )


class SynchronousEventPublisher(EventPublisher):
    """
    Synchronous event publisher for testing and development
    
    This publisher doesn't actually send events over the network,
    but logs them and can be used for testing purposes.
    """
    
    def __init__(self):
        self.published_events: List[BaseEvent] = []
    
    def publish(self, event: BaseEvent) -> bool:
        """Publish event synchronously (for testing)"""
        if not validate_event(event):
            logger.error(f"Invalid event: {event}")
            return False
        
        self.published_events.append(event)
        logger.info(f"Synchronously published event: {event.id} of type: {event.type}")
        return True
    
    def publish_batch(self, events: List[BaseEvent]) -> bool:
        """Publish batch of events synchronously"""
        success_count = 0
        for event in events:
            if self.publish(event):
                success_count += 1
        
        return success_count == len(events)
    
    def get_published_events(self) -> List[BaseEvent]:
        """Get all published events (for testing)"""
        return self.published_events.copy()
    
    def clear_published_events(self):
        """Clear published events (for testing)"""
        self.published_events.clear()


class EventPublisherFactory:
    """Factory for creating event publishers"""
    
    @staticmethod
    def create_publisher(publisher_type: str = "rabbitmq") -> EventPublisher:
        """
        Create an event publisher instance
        
        Args:
            publisher_type: Type of publisher ("rabbitmq" or "sync")
            
        Returns:
            EventPublisher instance
        """
        if publisher_type == "rabbitmq":
            return RabbitMQEventPublisher()
        elif publisher_type == "sync":
            return SynchronousEventPublisher()
        else:
            raise ValueError(f"Unknown publisher type: {publisher_type}")


# Global event publisher instance
_event_publisher: Optional[EventPublisher] = None


def get_event_publisher() -> EventPublisher:
    """Get the global event publisher instance"""
    global _event_publisher
    if _event_publisher is None:
        _event_publisher = EventPublisherFactory.create_publisher("rabbitmq")
    return _event_publisher


def set_event_publisher(publisher: EventPublisher):
    """Set the global event publisher instance (useful for testing)"""
    global _event_publisher
    _event_publisher = publisher


def publish_event(event: BaseEvent) -> bool:
    """Convenience function to publish an event using the global publisher"""
    publisher = get_event_publisher()
    return publisher.publish(event)


def publish_events(events: List[BaseEvent]) -> bool:
    """Convenience function to publish multiple events using the global publisher"""
    publisher = get_event_publisher()
    return publisher.publish_batch(events)
