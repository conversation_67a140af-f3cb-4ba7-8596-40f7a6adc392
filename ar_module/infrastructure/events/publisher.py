import logging

from kombu import Exchange, Producer
from treebo_commons.multitenancy.tenant_client import TenantClient
from treebo_commons.request_tracing.context import get_current_tenant_id

from ar_module.domain.events.entities.event_publisher_entity import (
    AREventPublisherEntity,
)
from ar_module.infrastructure.consumers.consumer_config import ARPublisherConfig
from ar_module.infrastructure.messaging.queue_service import BaseQueueService
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance()
class AREventPublisher(BaseQueueService):
    def _setup_entities(self):
        """Setup RabbitMQ exchange and producers"""
        config = ARPublisherConfig()
        self._integration_event_exchange = Exchange(
            name=config.exchange_name, type=config.exchange_type, durable=True
        )
        self._tenant_wise_producers = {}

        for tenant_id, conn in self.tenant_wise_connection.items():
            self._tenant_wise_producers[tenant_id] = Producer(
                channel=conn.channel(), exchange=self._integration_event_exchange
            )
            logger.info(f"AR event publisher setup completed for tenant: {tenant_id}")

    def publish(self, event: AREventPublisherEntity):
        current_tenant_id = get_current_tenant_id() or TenantClient.get_default_tenant()
        self._initialize()

        payload = self.get_event_payload(event=event)
        routing_key = self.get_event_routing_key(event=event)

        logger.debug(f"Publishing AR event {event.event_id}: {payload}")

        if current_tenant_id not in self._tenant_wise_producers:
            logger.error(
                f"RMQ Producer not configured for tenant_id: {current_tenant_id}"
            )
            raise Exception(
                f"RMQ Producer not configured for tenant_id: {current_tenant_id}"
            )

        self._publish(
            producer=self._tenant_wise_producers[current_tenant_id],
            payload=payload,
            routing_key=routing_key,
        )
        logger.info(
            f"Successfully published AR event {event.event_id} of type {event.event_type.value}"
        )

    @staticmethod
    def get_event_payload(event: AREventPublisherEntity):
        """Get the event payload for publishing"""
        return event.body

    @staticmethod
    def get_event_routing_key(event: AREventPublisherEntity):
        """Get the routing key for the event"""
        return event.event_type.routing_key
