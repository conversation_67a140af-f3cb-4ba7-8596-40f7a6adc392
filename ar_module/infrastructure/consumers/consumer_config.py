from treebo_commons.credentials.aws_secret_manager import AwsSecretManager
from treebo_commons.multitenancy.tenant_client import TenantClient


class CRSConfig(object):
    """
    Catalog Message Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "crs-events"
        self.exchange_type = "topic"
        self.queue_name = "armodule-crs-events"
        self.routing_keys = ["booking"]
        self.exclusive = False


class InterfaceExchangeConsumerConfig(object):
    """
    Interface ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "erp_reports"
        self.exchange_type = "topic"
        self.queue_name = "interface-event-armodule-queue"
        self.routing_keys = [
            "interface_exchange.erp.armodule",
        ]
        self.exclusive = False


class InterfaceExchangePublisherConfig(object):
    """
    Interface ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "erp_reports"
        self.exchange_type = "topic"
        self.queue_name = "interface-event-armodule-queue"
        self.routing_keys = [
            "armodule.erp.interface_exchange",
        ]
        self.exclusive = False


class CompanyProfileConsumerConfig(object):
    """
    CompanyProfile ExchangeConfig Jobs Queue Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "company-profile-events"
        self.exchange_type = "topic"
        self.queue_name = "company-profile-event-queue"
        self.routing_keys = ["#"]
        self.exclusive = False


class ARAsyncJobConsumerConfig(object):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "ar-events"
        self.exchange_type = "topic"
        self.queue_name = "ar-async-task-queue"
        self.routing_keys = [
            "task.#",
        ]
        self.exclusive = False


class ARAsyncJobPublisher(object):
    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.exchange_name = "ar-events"
        self.exchange_type = "topic"
        self.queue_name = "ar-async-task-queue"
        self.routing_keys = [
            "task.#",
        ]
        self.exclusive = False


class ARPublisherConfig(object):
    """
    AR Event Publisher Configuration
    """

    def __init__(self, tenant_id=TenantClient.get_default_tenant()):
        # self.rabbitmq_url = AwsSecretManager.get_rmq_url(tenant_id)
        self.rabbitmq_url = "amqp://guest:guest@localhost:5672/"
        self.exchange_name = "ar-event-publisher"
        self.exchange_type = "topic"
        self.queue_name = "ar-event-publisher-queue"
        self.routing_keys = [
            "ar.events.credit.#",  # Credit events
            "ar.events.debit.#",  # Debit events
        ]
        self.exclusive = False
