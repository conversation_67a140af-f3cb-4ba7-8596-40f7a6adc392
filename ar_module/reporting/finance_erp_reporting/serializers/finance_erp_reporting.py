from datetime import datetime
from typing import Union

from marshmallow import Schema, fields


def convert_date_format(date_str) -> Union[str, None]:
    try:
        return datetime.strptime(date_str, "%d-%m-%Y").strftime("%Y-%m-%d")
    except (ValueError, TypeError):
        return None


class DataKeyField(fields.Field):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        data_key = kwargs.get("data_key")
        if data_key is not None:
            if self.load_from is not None or self.dump_to is not None:
                raise RuntimeError(
                    "load_from and dump_to cannot be given when data_key is given"
                )
            self.load_from = data_key
            self.dump_to = data_key


class StringField(fields.String, DataKeyField):
    pass


class PaymentsPushSchema(Schema):
    posting_date = fields.Method(
        serialize="serialize_posting_date", dump_to="PostingDate", allow_none=True
    )
    payment_date = fields.Method(
        serialize="serialize_payment_date", dump_to="PaymentDate", allow_none=True
    )
    pg_charges = StringField(data_key="PGCharges", allow_none=True)
    pg_tax = StringField(data_key="PGTaxes", allow_none=True)
    pg_transaction_id = StringField(
        data_key="PaymentGatewayTransactionId", required=True
    )
    reference_number = StringField(data_key="BookingID", allow_none=True)
    hotel_code = StringField(data_key="HotelCode", allow_none=True)

    paid_by = StringField(data_key="PaidBy", allow_none=True)
    paid_to = StringField(data_key="PaidTo", allow_none=True)
    payment_type = StringField(data_key="PaymentType", allow_none=True)
    payment_amount = StringField(data_key="PaymentAmount", allow_none=True)
    paymode = StringField(data_key="Paymode", allow_none=True)
    paymode_type = StringField(data_key="PaymodeType", allow_none=True)
    payor_entity = StringField(data_key="PayorEntity", allow_none=True)
    athena_code = StringField(data_key="AthenaCode", allow_none=True)
    booker_entity = StringField(data_key="BookerEntity", allow_none=True)
    payor_name = StringField(data_key="PayorName", allow_none=True)
    hotel_name = StringField(data_key="HotelName", allow_none=True)
    invoice_id = StringField(data_key="InvoiceId", allow_none=True)
    check_in = fields.Method(
        serialize="serialize_check_in", dump_to="CheckInDate", allow_none=True
    )
    check_out = fields.Method(
        serialize="serialize_check_out", dump_to="CheckOutDate", allow_none=True
    )
    channel = StringField(data_key="Channel", allow_none=True)
    sub_channel = StringField(data_key="SubChannel", allow_none=True)
    seller_model = StringField(data_key="SellerModel", allow_none=True)
    original_booking_amount = StringField(
        data_key="OriginalBookingAmount", allow_none=True
    )
    is_advance = StringField(data_key="IsAdvance", allow_none=True)
    uu_id = StringField(data_key="UUID", allow_none=True)

    def serialize_posting_date(self, obj):
        return convert_date_format(obj.posting_date)

    def serialize_payment_date(self, obj):
        return convert_date_format(obj.payment_date)

    def serialize_check_in(self, obj):
        return convert_date_format(obj.check_in)

    def serialize_check_out(self, obj):
        return convert_date_format(obj.check_out)

    class Meta:
        ordered = True
        fields = (
            "hotel_code",
            "posting_date",
            "payment_date",
            "pg_transaction_id",
            "reference_number",
            "pg_charges",
            "pg_tax",
            "paid_by",
            "paid_to",
            "payment_type",
            "payment_amount",
            "paymode",
            "paymode_type",
            "payor_entity",
            "booker_entity",
            "athena_code",
            "payor_name",
            "hotel_name",
            "invoice_id",
            "check_in",
            "check_out",
            "channel",
            "sub_channel",
            "seller_model",
            "original_booking_amount",
            "is_advance",
            "uu_id",
        )
