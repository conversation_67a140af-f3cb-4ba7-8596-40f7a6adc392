from ar_module.domain.constants import PrivilegeCode
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.create_credit_reversal_facts import (
    CreateCreditReversalFacts,
)
from ar_module.domain.policy.rules.base import BaseRule
from ar_module.exceptions import PolicyAuthException


class CreateCreditReversalRule(BaseRule):
    def allow(self, facts: CreateCreditReversalFacts, privileges=None):
        # if PrivilegeCode.CREATE_CREDIT_REVERSAL not in privileges:
        #     raise PolicyAuthException(
        #         error=PolicyError.CREATE_CREDIT_REVERSAL_NOT_ALLOWED
        #     )
        return True
