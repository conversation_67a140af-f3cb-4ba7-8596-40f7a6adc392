from marshmallow import Schema, fields, post_dump
from treebo_commons.money.money_field import Money<PERSON><PERSON>

from ar_module.api.schemas import validate_positive_integer


class CreditTransactionPublisherSchema(Schema):
    payment_credit_id = fields.String(required=True)
    amount_in_base_currency = MoneyField(validate=validate_positive_integer)
    amount_in_credit_currency = MoneyField(validate=validate_positive_integer)
    remarks = fields.String(allow_none=True)
    refund_credit_id = fields.String()

    # Fields for payment_credit details
    credit_payment_mode = fields.String(allow_none=True)
    credit_date = fields.Date(allow_none=True)
    credit_reference_number = fields.String(allow_none=True)
    credit_amount_in_base_currency = MoneyField(allow_none=True)
    credit_amount_in_credit_currency = MoneyField(allow_none=True)
    credit_unused_amount = MoneyField(allow_none=True)
    credit_refunded_amount = MoneyField(allow_none=True)


class CreditPublisherSchema(Schema):
    credit_id = fields.String()
    debtor_id = fields.String()
    credit_type = fields.String()
    status = fields.String()
    date = fields.Date()
    amount_in_credit_currency = MoneyField()
    amount_in_base_currency = MoneyField()
    reference_number = fields.String()
    reference_id = fields.String()
    mode_of_credit = fields.String()
    unused_credit_amount = MoneyField()
    refunded_amount = MoneyField()
    used_to_auto_settle_debit = fields.Boolean()
    created_at = fields.LocalDateTime()
    approval_document = fields.String()
    tenant_id = fields.String(allow_none=True)
    credit_reversals = fields.Nested(
        CreditTransactionPublisherSchema, many=True, allow_none=True
    )

    @post_dump
    def rename_credit_reversals(self, data, **kwargs):
        if "credit_reversals" in data:
            data["credit_transactions"] = data.pop("credit_reversals")
        return data