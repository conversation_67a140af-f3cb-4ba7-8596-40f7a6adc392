# coding=utf-8
"""
Debit-related events for AR Module Event Framework
"""
from datetime import datetime
from typing import Any, Dict, Optional

from .base_event import BaseEvent, EntityType, EventSource, UserAction


class DebitCreatedEvent(BaseEvent):
    """Event emitted when a debit is created"""
    
    def __init__(
        self,
        debit_id: str,
        recorded_via: EventSource,
        debit_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        super().__init__(
            entity_type=EntityType.DEBIT,
            user_action=UserAction.CREATE,
            recorded_via=recorded_via,
            entity_id=debit_id,
            tenant_id=tenant_id,
            user_id=user_id,
            hotel_id=hotel_id,
            data=debit_data,
            event_id=event_id,
            timestamp=timestamp
        )
    
    def _get_event_type(self) -> str:
        return "com.treebo.ar.debit.created"


class DebitUpdatedEvent(BaseEvent):
    """Event emitted when a debit is updated"""
    
    def __init__(
        self,
        debit_id: str,
        recorded_via: EventSource,
        update_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        super().__init__(
            entity_type=EntityType.DEBIT,
            user_action=UserAction.UPDATE,
            recorded_via=recorded_via,
            entity_id=debit_id,
            tenant_id=tenant_id,
            user_id=user_id,
            hotel_id=hotel_id,
            data=update_data,
            event_id=event_id,
            timestamp=timestamp
        )
    
    def _get_event_type(self) -> str:
        return "com.treebo.ar.debit.updated"


class DebitCancelledEvent(BaseEvent):
    """Event emitted when a debit is cancelled"""
    
    def __init__(
        self,
        debit_id: str,
        recorded_via: EventSource,
        cancellation_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        super().__init__(
            entity_type=EntityType.DEBIT,
            user_action=UserAction.CANCEL,
            recorded_via=recorded_via,
            entity_id=debit_id,
            tenant_id=tenant_id,
            user_id=user_id,
            hotel_id=hotel_id,
            data=cancellation_data,
            event_id=event_id,
            timestamp=timestamp
        )
    
    def _get_event_type(self) -> str:
        return "com.treebo.ar.debit.cancelled"


# Factory functions for creating debit events
def create_debit_created_event(
    debit_id: str,
    recorded_via: EventSource,
    debit_data: Dict[str, Any],
    **kwargs
) -> DebitCreatedEvent:
    """Factory function to create a debit created event"""
    return DebitCreatedEvent(
        debit_id=debit_id,
        recorded_via=recorded_via,
        debit_data=debit_data,
        **kwargs
    )


def create_debit_updated_event(
    debit_id: str,
    recorded_via: EventSource,
    update_data: Dict[str, Any],
    **kwargs
) -> DebitUpdatedEvent:
    """Factory function to create a debit updated event"""
    return DebitUpdatedEvent(
        debit_id=debit_id,
        recorded_via=recorded_via,
        update_data=update_data,
        **kwargs
    )


def create_debit_cancelled_event(
    debit_id: str,
    recorded_via: EventSource,
    cancellation_data: Dict[str, Any],
    **kwargs
) -> DebitCancelledEvent:
    """Factory function to create a debit cancelled event"""
    return DebitCancelledEvent(
        debit_id=debit_id,
        recorded_via=recorded_via,
        cancellation_data=cancellation_data,
        **kwargs
    )
