from enum import Enum
from typing import Any, Dict

from object_registry import register_instance


class AREventPublisherType(Enum):
    CREDIT_CREATED = "credit.created"
    CREDIT_CANCELLED = "credit.cancelled"
    CREDIT_REVERSED = "credit.reversed"
    CREDIT_REVERSAL_CANCELLED = "credit.reversal.cancelled"
    DEBIT_CREATED = "debit.created"
    DEBIT_UPDATED = "debit.updated"
    DEBIT_CANCELLED = "debit.cancelled"

    @property
    def routing_key(self):
        """Get routing key for RabbitMQ"""
        return f"ar.events.{self.value}"


class AREventPublisherDTO:
    def __init__(
        self,
        event_type: AREventPublisherType,
        entity_id: str,
        body: Dict[str, Any],
    ):
        self.event_type = event_type
        self.entity_id = entity_id
        self.body = body
