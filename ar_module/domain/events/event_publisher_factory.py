from typing import Any, Dict

from ar_module.domain.events.dtos.event_publisher_dto import (
    AREventPublisherDTO,
    AREventPublisherType,
)
from ar_module.domain.events.entities.event_publisher_entity import (
    AREventPublisherEntity,
)


class ARIntegrationEventFactory:
    @staticmethod
    def create_integration_event(
        event_dto: AREventPublisherDTO,
    ) -> AREventPublisherEntity:
        return AREventPublisherEntity(
            event_type=event_dto.event_type,
            body=event_dto.body,
            event_id=event_dto.entity_id,
        )

    @staticmethod
    def create_credit_created_event_dto(
        entity_id: str,
        body: Dict[str, Any],
    ) -> AREventPublisherDTO:
        """Create a credit created event DTO"""
        return AREventPublisherDTO(
            event_type=AREventPublisherType.CREDIT_CREATED,
            entity_id=entity_id,
            body=body,
        )

    @staticmethod
    def create_credit_reversal_event_dto(
        entity_id: str,
        body: Dict[str, Any],
    ) -> AREventPublisherDTO:
        return AREventPublisherDTO(
            event_type=AREventPublisherType.CREDIT_REVERSED,
            entity_id=entity_id,
            body=body,
        )

    @staticmethod
    def create_credit_reversal_cancelled_event_dto(
        entity_id: str,
        body: Dict[str, Any],
    ) -> AREventPublisherDTO:
        return AREventPublisherDTO(
            event_type=AREventPublisherType.CREDIT_REVERSAL_CANCELLED,
            entity_id=entity_id,
            body=body,
        )

    @staticmethod
    def create_debit_created_event_dto(
        entity_id: str,
        body: Dict[str, Any],
    ) -> AREventPublisherDTO:
        return AREventPublisherDTO(
            event_type=AREventPublisherType.DEBIT_CREATED,
            entity_id=entity_id,
            body=body,
        )
