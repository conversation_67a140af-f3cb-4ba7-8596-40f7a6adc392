# coding=utf-8
"""
Event Schema Definitions for AR Module Event Framework

This module defines the JSON schema for AR events following CloudEvents specification
with AR-specific extensions.
"""

# CloudEvents v1.0 compliant schema with AR extensions
AR_EVENT_SCHEMA = {
    "$schema": "http://json-schema.org/draft-07/schema#",
    "title": "AR Event Schema",
    "description": "Schema for AR Module events following CloudEvents specification",
    "type": "object",
    "required": [
        "id",
        "specversion", 
        "type",
        "source",
        "time",
        "entity_type",
        "user_action",
        "recorded_via",
        "entity_id",
        "tenant_id"
    ],
    "properties": {
        # CloudEvents required attributes
        "id": {
            "type": "string",
            "description": "Unique identifier for the event",
            "pattern": "^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{4}-[a-fA-F0-9]{12}$"
        },
        "specversion": {
            "type": "string",
            "description": "Version of CloudEvents specification",
            "const": "1.0"
        },
        "type": {
            "type": "string",
            "description": "Type of event",
            "pattern": "^com\\.treebo\\.ar\\.[a-z_]+\\.[a-z_]+$"
        },
        "source": {
            "type": "string",
            "description": "Identifies the context in which an event happened",
            "pattern": "^ar-module/[a-z_]+$"
        },
        "time": {
            "type": "string",
            "description": "Timestamp when the event occurred",
            "format": "date-time"
        },
        "subject": {
            "type": "string",
            "description": "Subject of the event",
            "pattern": "^[a-z_]+/[a-zA-Z0-9-_]+$"
        },
        "datacontenttype": {
            "type": "string",
            "description": "Content type of the data",
            "const": "application/json"
        },
        
        # AR-specific required attributes
        "entity_type": {
            "type": "string",
            "description": "Type of AR entity",
            "enum": ["credit", "credit_reversal", "debit", "debtor", "settlement"]
        },
        "user_action": {
            "type": "string", 
            "description": "Action performed by user",
            "enum": ["create", "update", "delete", "cancel", "reverse", "approve", "reject"]
        },
        "recorded_via": {
            "type": "string",
            "description": "Source/method of recording the event",
            "enum": ["api", "bulk_upload", "system", "integration", "migration", "manual"]
        },
        "entity_id": {
            "type": "string",
            "description": "Unique identifier of the entity",
            "minLength": 1
        },
        "tenant_id": {
            "type": "string",
            "description": "Tenant identifier",
            "minLength": 1
        },
        
        # Optional context attributes
        "user_id": {
            "type": "string",
            "description": "User who performed the action"
        },
        "hotel_id": {
            "type": "string",
            "description": "Hotel identifier"
        },
        
        # Event data payload
        "data": {
            "type": "object",
            "description": "Event-specific data payload",
            "additionalProperties": True
        }
    },
    "additionalProperties": False
}

# Specific schemas for different event types
CREDIT_CREATED_EVENT_SCHEMA = {
    **AR_EVENT_SCHEMA,
    "title": "Credit Created Event Schema",
    "properties": {
        **AR_EVENT_SCHEMA["properties"],
        "type": {"const": "com.treebo.ar.credit.created"},
        "entity_type": {"const": "credit"},
        "user_action": {"const": "create"},
        "data": {
            "type": "object",
            "description": "Credit creation data",
            "properties": {
                "credit_amount": {"type": "number"},
                "currency": {"type": "string"},
                "debtor_id": {"type": "string"},
                "payment_mode": {"type": "string"},
                "credit_date": {"type": "string", "format": "date"},
                "reference_number": {"type": "string"},
                "description": {"type": "string"}
            },
            "required": ["credit_amount", "currency", "debtor_id"]
        }
    }
}

CREDIT_REVERSAL_EVENT_SCHEMA = {
    **AR_EVENT_SCHEMA,
    "title": "Credit Reversal Event Schema", 
    "properties": {
        **AR_EVENT_SCHEMA["properties"],
        "type": {"const": "com.treebo.ar.credit.reversed"},
        "entity_type": {"const": "credit_reversal"},
        "user_action": {"const": "create"},
        "data": {
            "type": "object",
            "description": "Credit reversal data",
            "properties": {
                "original_credit_id": {"type": "string"},
                "reversal_amount": {"type": "number"},
                "currency": {"type": "string"},
                "reversal_date": {"type": "string", "format": "date"},
                "reason": {"type": "string"},
                "reference_number": {"type": "string"}
            },
            "required": ["original_credit_id", "reversal_amount", "currency"]
        }
    }
}

DEBIT_CREATED_EVENT_SCHEMA = {
    **AR_EVENT_SCHEMA,
    "title": "Debit Created Event Schema",
    "properties": {
        **AR_EVENT_SCHEMA["properties"],
        "type": {"const": "com.treebo.ar.debit.created"},
        "entity_type": {"const": "debit"},
        "user_action": {"const": "create"},
        "data": {
            "type": "object",
            "description": "Debit creation data",
            "properties": {
                "debit_amount": {"type": "number"},
                "currency": {"type": "string"},
                "debtor_id": {"type": "string"},
                "debit_date": {"type": "string", "format": "date"},
                "due_date": {"type": "string", "format": "date"},
                "invoice_number": {"type": "string"},
                "description": {"type": "string"},
                "debit_type": {"type": "string"}
            },
            "required": ["debit_amount", "currency", "debtor_id"]
        }
    }
}

# Event type to schema mapping
EVENT_SCHEMAS = {
    "com.treebo.ar.credit.created": CREDIT_CREATED_EVENT_SCHEMA,
    "com.treebo.ar.credit.reversed": CREDIT_REVERSAL_EVENT_SCHEMA,
    "com.treebo.ar.credit.reversal.cancelled": AR_EVENT_SCHEMA,
    "com.treebo.ar.credit.cancelled": AR_EVENT_SCHEMA,
    "com.treebo.ar.debit.created": DEBIT_CREATED_EVENT_SCHEMA,
    "com.treebo.ar.debit.updated": AR_EVENT_SCHEMA,
    "com.treebo.ar.debit.cancelled": AR_EVENT_SCHEMA
}


def get_event_schema(event_type: str) -> dict:
    """Get the JSON schema for a specific event type"""
    return EVENT_SCHEMAS.get(event_type, AR_EVENT_SCHEMA)


def validate_event_schema(event_data: dict) -> tuple[bool, list]:
    """
    Validate event data against its schema
    
    Returns:
        tuple: (is_valid, errors)
    """
    try:
        import jsonschema
        
        event_type = event_data.get("type")
        schema = get_event_schema(event_type)
        
        jsonschema.validate(event_data, schema)
        return True, []
        
    except jsonschema.ValidationError as e:
        return False, [str(e)]
    except Exception as e:
        return False, [f"Schema validation error: {str(e)}"]


# Example event payloads for documentation
EXAMPLE_CREDIT_CREATED_EVENT = {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "specversion": "1.0",
    "type": "com.treebo.ar.credit.created",
    "source": "ar-module/credit",
    "time": "2024-01-15T10:30:00Z",
    "subject": "credit/CR-2024-001",
    "datacontenttype": "application/json",
    "entity_type": "credit",
    "user_action": "create",
    "recorded_via": "api",
    "entity_id": "CR-2024-001",
    "tenant_id": "tenant-123",
    "user_id": "user-456",
    "hotel_id": "hotel-789",
    "data": {
        "credit_amount": 1000.00,
        "currency": "INR",
        "debtor_id": "debtor-123",
        "payment_mode": "cash",
        "credit_date": "2024-01-15",
        "reference_number": "REF-001",
        "description": "Payment received"
    }
}

EXAMPLE_DEBIT_CREATED_EVENT = {
    "id": "550e8400-e29b-41d4-a716-446655440001",
    "specversion": "1.0", 
    "type": "com.treebo.ar.debit.created",
    "source": "ar-module/debit",
    "time": "2024-01-15T10:30:00Z",
    "subject": "debit/DB-2024-001",
    "datacontenttype": "application/json",
    "entity_type": "debit",
    "user_action": "create",
    "recorded_via": "api",
    "entity_id": "DB-2024-001",
    "tenant_id": "tenant-123",
    "user_id": "user-456",
    "hotel_id": "hotel-789",
    "data": {
        "debit_amount": 5000.00,
        "currency": "INR",
        "debtor_id": "debtor-123",
        "debit_date": "2024-01-15",
        "due_date": "2024-02-15",
        "invoice_number": "INV-001",
        "description": "Room charges",
        "debit_type": "accommodation"
    }
}
