from typing import Any, Dict

from ar_module.domain.events.dtos.event_publisher_dto import AREventPublisherType


class AREventPublisherEntity:
    def __init__(
        self,
        event_type: AREventPublisherType,
        body: Dict[str, Any],
        event_id: str,
    ):
        self.event_type = event_type
        self.body = body
        self.event_id = event_id

    def __str__(self):
        return (
            f"AREventPublisherEntity(id={self.event_id}, type={self.event_type.value})"
        )

    def __repr__(self):
        return self.__str__()
