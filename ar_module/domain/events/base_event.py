# coding=utf-8
"""
Base Event Classes for AR Module Event Framework

This module defines the base event structure inspired by CloudEvents specification
with AR-specific attributes: entity_type, source (recorded_via), and user_action.
"""
import uuid
from abc import ABC, abstractmethod
from datetime import datetime
from enum import Enum
from typing import Any, Dict, Optional

from treebo_commons.request_tracing.context import get_current_tenant_id


class EntityType(Enum):
    """Entity types for AR events"""
    CREDIT = "credit"
    CREDIT_REVERSAL = "credit_reversal"
    DEBIT = "debit"
    DEBTOR = "debtor"
    SETTLEMENT = "settlement"


class UserAction(Enum):
    """User actions that trigger events"""
    CREATE = "create"
    UPDATE = "update"
    DELETE = "delete"
    CANCEL = "cancel"
    REVERSE = "reverse"
    APPROVE = "approve"
    REJECT = "reject"


class EventSource(Enum):
    """Source of the event (recorded_via)"""
    API = "api"
    BULK_UPLOAD = "bulk_upload"
    SYSTEM = "system"
    INTEGRATION = "integration"
    MIGRATION = "migration"
    MANUAL = "manual"


class BaseEvent(ABC):
    """
    Base event class following CloudEvents specification pattern
    with AR-specific attributes.
    
    Required CloudEvents attributes:
    - id: Unique identifier for the event
    - source: Identifies the context in which an event happened
    - specversion: Version of CloudEvents specification
    - type: Type of event
    - time: Timestamp when the event occurred
    
    AR-specific attributes:
    - entity_type: Type of AR entity (credit, debit, etc.)
    - user_action: Action performed by user
    - recorded_via: Source/method of recording (API, bulk upload, etc.)
    """
    
    SPEC_VERSION = "1.0"
    
    def __init__(
        self,
        entity_type: EntityType,
        user_action: UserAction,
        recorded_via: EventSource,
        entity_id: str,
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        data: Optional[Dict[str, Any]] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        self.id = event_id or str(uuid.uuid4())
        self.specversion = self.SPEC_VERSION
        self.type = self._get_event_type()
        self.source = f"ar-module/{entity_type.value}"
        self.time = timestamp or datetime.utcnow()
        
        # AR-specific attributes
        self.entity_type = entity_type
        self.user_action = user_action
        self.recorded_via = recorded_via
        self.entity_id = entity_id
        
        # Context attributes
        self.tenant_id = tenant_id or get_current_tenant_id()
        self.user_id = user_id
        self.hotel_id = hotel_id
        
        # Event data payload
        self.data = data or {}
        
        # Additional metadata
        self.datacontenttype = "application/json"
        self.subject = f"{entity_type.value}/{entity_id}"
    
    @abstractmethod
    def _get_event_type(self) -> str:
        """Return the specific event type string"""
        pass
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert event to dictionary representation"""
        event_dict = {
            "id": self.id,
            "specversion": self.specversion,
            "type": self.type,
            "source": self.source,
            "time": self.time.isoformat() + "Z",
            "subject": self.subject,
            "datacontenttype": self.datacontenttype,
            
            # AR-specific attributes
            "entity_type": self.entity_type.value,
            "user_action": self.user_action.value,
            "recorded_via": self.recorded_via.value,
            "entity_id": self.entity_id,
            
            # Context
            "tenant_id": self.tenant_id,
        }
        
        # Add optional context attributes
        if self.user_id:
            event_dict["user_id"] = self.user_id
        if self.hotel_id:
            event_dict["hotel_id"] = self.hotel_id
            
        # Add data payload
        if self.data:
            event_dict["data"] = self.data
            
        return event_dict
    
    def to_json(self) -> str:
        """Convert event to JSON string"""
        import json
        return json.dumps(self.to_dict(), default=str)
    
    @classmethod
    def from_dict(cls, event_dict: Dict[str, Any]) -> 'BaseEvent':
        """Create event instance from dictionary"""
        # This is a factory method that would need to be implemented
        # by specific event classes or a factory class
        raise NotImplementedError("Subclasses must implement from_dict method")
    
    def __str__(self) -> str:
        return f"{self.__class__.__name__}(id={self.id}, type={self.type}, entity_id={self.entity_id})"
    
    def __repr__(self) -> str:
        return self.__str__()


class AREvent(BaseEvent):
    """
    Generic AR event class for common events
    """
    
    def _get_event_type(self) -> str:
        return f"com.treebo.ar.{self.entity_type.value}.{self.user_action.value}"


# Event validation utilities
def validate_event(event: BaseEvent) -> bool:
    """Validate that an event has all required attributes"""
    required_attrs = ['id', 'specversion', 'type', 'source', 'time', 'entity_type', 'user_action', 'recorded_via']
    
    for attr in required_attrs:
        if not hasattr(event, attr) or getattr(event, attr) is None:
            return False
    
    return True


def create_event_metadata(
    entity_type: EntityType,
    user_action: UserAction,
    recorded_via: EventSource,
    entity_id: str,
    **kwargs
) -> Dict[str, Any]:
    """Helper function to create event metadata"""
    return {
        'entity_type': entity_type,
        'user_action': user_action,
        'recorded_via': recorded_via,
        'entity_id': entity_id,
        **kwargs
    }
