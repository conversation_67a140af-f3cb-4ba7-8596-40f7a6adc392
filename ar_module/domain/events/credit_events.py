# coding=utf-8
"""
Credit-related events for AR Module Event Framework
"""
from datetime import datetime
from typing import Any, Dict, Optional

from .base_event import BaseEvent, EntityType, EventSource, UserAction


class CreditCreatedEvent(BaseEvent):
    """Event emitted when a credit is created"""
    
    def __init__(
        self,
        credit_id: str,
        recorded_via: EventSource,
        credit_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        super().__init__(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=recorded_via,
            entity_id=credit_id,
            tenant_id=tenant_id,
            user_id=user_id,
            hotel_id=hotel_id,
            data=credit_data,
            event_id=event_id,
            timestamp=timestamp
        )
    
    def _get_event_type(self) -> str:
        return "com.treebo.ar.credit.created"


class CreditReversalCreatedEvent(BaseEvent):
    """Event emitted when a credit reversal is created"""
    
    def __init__(
        self,
        credit_reversal_id: str,
        credit_id: str,
        recorded_via: EventSource,
        reversal_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        # Add original credit ID to the data
        reversal_data = reversal_data.copy()
        reversal_data['original_credit_id'] = credit_id
        
        super().__init__(
            entity_type=EntityType.CREDIT_REVERSAL,
            user_action=UserAction.CREATE,
            recorded_via=recorded_via,
            entity_id=credit_reversal_id,
            tenant_id=tenant_id,
            user_id=user_id,
            hotel_id=hotel_id,
            data=reversal_data,
            event_id=event_id,
            timestamp=timestamp
        )
        
        self.original_credit_id = credit_id
    
    def _get_event_type(self) -> str:
        return "com.treebo.ar.credit.reversed"


class CreditReversalCancelledEvent(BaseEvent):
    """Event emitted when a credit reversal is cancelled"""
    
    def __init__(
        self,
        credit_reversal_id: str,
        credit_id: str,
        recorded_via: EventSource,
        cancellation_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        # Add original credit ID to the data
        cancellation_data = cancellation_data.copy()
        cancellation_data['original_credit_id'] = credit_id
        
        super().__init__(
            entity_type=EntityType.CREDIT_REVERSAL,
            user_action=UserAction.CANCEL,
            recorded_via=recorded_via,
            entity_id=credit_reversal_id,
            tenant_id=tenant_id,
            user_id=user_id,
            hotel_id=hotel_id,
            data=cancellation_data,
            event_id=event_id,
            timestamp=timestamp
        )
        
        self.original_credit_id = credit_id
    
    def _get_event_type(self) -> str:
        return "com.treebo.ar.credit.reversal.cancelled"


class CreditCancelledEvent(BaseEvent):
    """Event emitted when a credit is cancelled"""
    
    def __init__(
        self,
        credit_id: str,
        recorded_via: EventSource,
        cancellation_data: Dict[str, Any],
        tenant_id: Optional[str] = None,
        user_id: Optional[str] = None,
        hotel_id: Optional[str] = None,
        event_id: Optional[str] = None,
        timestamp: Optional[datetime] = None
    ):
        super().__init__(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CANCEL,
            recorded_via=recorded_via,
            entity_id=credit_id,
            tenant_id=tenant_id,
            user_id=user_id,
            hotel_id=hotel_id,
            data=cancellation_data,
            event_id=event_id,
            timestamp=timestamp
        )
    
    def _get_event_type(self) -> str:
        return "com.treebo.ar.credit.cancelled"


# Factory functions for creating credit events
def create_credit_created_event(
    credit_id: str,
    recorded_via: EventSource,
    credit_data: Dict[str, Any],
    **kwargs
) -> CreditCreatedEvent:
    """Factory function to create a credit created event"""
    return CreditCreatedEvent(
        credit_id=credit_id,
        recorded_via=recorded_via,
        credit_data=credit_data,
        **kwargs
    )


def create_credit_reversal_event(
    credit_reversal_id: str,
    credit_id: str,
    recorded_via: EventSource,
    reversal_data: Dict[str, Any],
    **kwargs
) -> CreditReversalCreatedEvent:
    """Factory function to create a credit reversal event"""
    return CreditReversalCreatedEvent(
        credit_reversal_id=credit_reversal_id,
        credit_id=credit_id,
        recorded_via=recorded_via,
        reversal_data=reversal_data,
        **kwargs
    )


def create_credit_reversal_cancelled_event(
    credit_reversal_id: str,
    credit_id: str,
    recorded_via: EventSource,
    cancellation_data: Dict[str, Any],
    **kwargs
) -> CreditReversalCancelledEvent:
    """Factory function to create a credit reversal cancelled event"""
    return CreditReversalCancelledEvent(
        credit_reversal_id=credit_reversal_id,
        credit_id=credit_id,
        recorded_via=recorded_via,
        cancellation_data=cancellation_data,
        **kwargs
    )


def create_credit_cancelled_event(
    credit_id: str,
    recorded_via: EventSource,
    cancellation_data: Dict[str, Any],
    **kwargs
) -> CreditCancelledEvent:
    """Factory function to create a credit cancelled event"""
    return CreditCancelledEvent(
        credit_id=credit_id,
        recorded_via=recorded_via,
        cancellation_data=cancellation_data,
        **kwargs
    )
