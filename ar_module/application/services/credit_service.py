import logging

from marshmallow import ValidationError
from treebo_commons.money import Money

from ar_module.application.dtos.credit_dto import CreditDto, CreditReversalDto
from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.credit_reversal_service import CreditReversalService
from ar_module.application.services.debtor_service import DebtorService
from ar_module.application.services.settlement_service import SettlementService
from ar_module.application.services.user_defined_enums import UserDefinedEnums
from ar_module.application.utils import (
    validate_credit_date,
    validate_credit_type,
    validate_payment_mode,
    validate_payment_mode_based_on_role,
    validate_refund_mode,
)
from ar_module.common.slack_alert_helper import SlackAlert
from ar_module.common.utils import calculate_due_date_based_on_settlement_frequency
from ar_module.core.common.globals import global_context
from ar_module.domain.constants import (
    ARModuleConfigs,
    AuditType,
    CreditStatus,
    CreditType,
    DebitType,
    UserType,
)
from ar_module.domain.dtos.credit_search_query import CreditSearchQuery
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.factories.credit_factory import CreditFactory
from ar_module.domain.factories.credit_reversal_factory import CreditReversalFactory
from ar_module.domain.factories.debit_factory import DebitFactory
from ar_module.domain.policy.engine import RuleEngine
from ar_module.domain.policy.facts.cancel_credit_facts import CancelCreditFacts
from ar_module.domain.policy.facts.create_credit_facts import CreateCreditFacts
from ar_module.domain.policy.facts.create_credit_reversal_facts import (
    CreateCreditReversalFacts,
)
from ar_module.domain.policy.facts.view_credit_facts import ViewCreditFacts
from ar_module.domain.value_objects.amount import Amount
from ar_module.infrastructure.database.repositories.credit.credit_ref_number_series_repository import (
    CreditRefNumberSeriesRepository,
)
from ar_module.infrastructure.database.repositories.credit.credit_repository import (
    CreditRepository,
)
from ar_module.infrastructure.database.repositories.credit.credit_reversal_repository import (
    CreditReversalRepository,
)
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.database.repositories.settlement.settlement_repository import (
    SettlementRepository,
)
from ar_module.infrastructure.database.repositories.user.user_repository import (
    UserRepository,
)
from ar_module.infrastructure.db_transaction import unit_of_work
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.cashier_client import CashierClient
from ar_module.infrastructure.events.event_service import AREventService
from ar_module.domain.events.base_event import EventSource
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        CreditRepository,
        CreditRefNumberSeriesRepository,
        CashierClient,
        DebtorService,
        SettlementService,
        AuditTrailService,
        SettlementRepository,
        DebtorRepository,
        UserRepository,
        DebitRepository,
        TenantSettings,
        CreditReversalRepository,
        CreditReversalService,
        UserDefinedEnums,
        AREventService,
    ]
)
class CreditService(object):
    def __init__(
        self,
        credit_repo: CreditRepository,
        credit_ref_number_series_repo: CreditRefNumberSeriesRepository,
        cashier_client: CashierClient,
        debtor_service: DebtorService,
        settlement_service: SettlementService,
        audit_trail_service: AuditTrailService,
        settlement_repo: SettlementRepository,
        debtor_repo: DebtorRepository,
        user_repo: UserRepository,
        debit_repo: DebitRepository,
        tenant_settings: TenantSettings,
        credit_reversal_repo: CreditReversalRepository,
        credit_reversal_service: CreditReversalService,
        user_defined_enums: UserDefinedEnums,
        event_service: AREventService,
    ):
        self.credit_repo = credit_repo
        self.credit_ref_number_series_repo = credit_ref_number_series_repo
        self.cashier_client = cashier_client
        self.audit_trail_service = audit_trail_service
        self.debtor_service = debtor_service
        self.settlement_service = settlement_service
        self.settlement_repo = settlement_repo
        self.debit_repo = debit_repo
        self.user_repo = user_repo
        self.debtor_repo = debtor_repo
        self.tenant_settings = tenant_settings
        self.tenant_id = global_context.tenant_id
        self.credit_reversal_repo = credit_reversal_repo
        self.credit_reversal_service = credit_reversal_service
        self.user_defined_enums = user_defined_enums
        self.event_service = event_service

    @unit_of_work
    def create_new_credit(self, credit_dto: CreditDto, user_data=None):
        RuleEngine.action_allowed(
            action="create_credit",
            facts=CreateCreditFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtor_aggregate = self.debtor_repo.load(
            credit_dto.debtor_id, user_data.hotel_id, is_hotel_level_ar_configured
        )
        validate_credit_date(credit_dto.date)
        validate_payment_mode(
            credit_dto.mode_of_credit, debtor_aggregate.debtor_code, user_data=user_data
        )

        if not credit_dto.reference_number:
            # Create Payment Reference Number in format:
            #   <debtor_code>P<payment_date_MMDDYY><payment_serial_number_of_day_001_or_002>
            credit_dto.reference_number = (
                self.credit_ref_number_series_repo.get_next_credit_reference_number(
                    debtor_aggregate, credit_dto.date
                )
            )

        credit_aggregate = CreditFactory.create_new_credit(credit_dto=credit_dto)
        debit_aggregates = None
        settlements = None
        debit_id_to_new_settlements_mapping = None
        if credit_dto.settlements:
            (
                debit_aggregates,
                settlements,
                debit_id_to_new_settlements_mapping,
                tds_credit_aggregates,
                cancelled_settlements_debit_aggregates,
                debit_id_to_unmapped_settlements_mapping,
            ) = self.settlement_service.create_settlements(
                credit_aggregate, credit_dto.settlements
            )
            if tds_credit_aggregates:
                self.credit_repo.save_all(tds_credit_aggregates)

        self._handle_auto_debits(credit_aggregate)
        self.credit_repo.save(credit_aggregate)
        if debit_aggregates:
            self.debit_repo.update_all(debit_aggregates)

        self.audit_trail_service.create_credit_audit_trail(
            debtor_aggregate,
            credit_aggregate,
            debtor_aggregate.hotel_id,
            AuditType.CREDIT_CREATED,
            debit_aggregates=debit_aggregates,
            user_data=user_data,
        )
        if settlements:
            self._create_audit_for_settlement(
                debit_aggregates,
                credit_aggregate,
                debtor_aggregate,
                debit_id_to_new_settlements_mapping,
                user_data,
            )

        if is_hotel_level_ar_configured:
            is_cashiering_enabled = self.tenant_settings.get_setting_value(
                ARModuleConfigs.CASHIERING_ENABLED, hotel_id=debtor_aggregate.hotel_id
            )
            if (
                is_cashiering_enabled
                and debtor_aggregate.is_linked_to_hotel()
                and credit_aggregate.credit.credit_type == CreditType.PAYMENT
            ):
                self.cashier_client.add_payment_to_cashier(
                    debtor_aggregate.hotel_id, credit_aggregate.credit
                )

        # Emit credit created event
        try:
            credit_data = {
                "credit_amount": float(credit_aggregate.credit.amount.value),
                "currency": credit_aggregate.credit.amount.currency,
                "debtor_id": credit_aggregate.credit.debtor_id,
                "payment_mode": credit_aggregate.credit.mode_of_credit,
                "credit_date": credit_aggregate.credit.date.isoformat(),
                "reference_number": credit_aggregate.credit.reference_number,
                "description": credit_aggregate.credit.description or "",
                "credit_type": credit_aggregate.credit.credit_type.value if credit_aggregate.credit.credit_type else None,
                "status": credit_aggregate.credit.status.value if credit_aggregate.credit.status else None
            }

            self.event_service.emit_credit_created(
                credit_id=credit_aggregate.credit.id,
                recorded_via=EventSource.API,  # Assuming API for now, could be parameterized
                credit_data=credit_data,
                tenant_id=self.tenant_id,
                user_id=user_data.user_id if user_data else None,
                hotel_id=debtor_aggregate.hotel_id
            )
        except Exception as e:
            logger.warning(f"Failed to emit credit created event for credit {credit_aggregate.credit.id}: {e}")

        return credit_aggregate

    def _handle_auto_debits(self, credit_aggregate):
        try:
            debtor_configs = self.tenant_settings.get_debtor_configs()
            self.record_auto_debit_to_corresponding_payment_mode_debtor_if_needed(
                credit_aggregate,
                debtor_configs,
            )
        except Exception as e:
            msg = "Exception: {0} occurred while creating debit under associated bank debtor for credit: {1}".format(
                str(e), credit_aggregate.credit.reference_number
            )
            logger.exception(msg)
            SlackAlert.send_alert(msg, tenant_id=self.tenant_id)

    def search_credits(
        self,
        credit_search_query: CreditSearchQuery,
        get_total_count=False,
        user_data=None,
    ):
        credit_aggregates, total_credits = [], 0
        RuleEngine.action_allowed(
            action="view_credit",
            facts=ViewCreditFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtors = self._get_debtors(
            credit_search_query.debtor_id, is_hotel_level_ar_configured, user_data
        )
        credit_search_query.debtor_ids = (
            sorted(d.debtor_id for d in debtors) if debtors else None
        )

        if credit_search_query.debtor_id and not debtors:
            return credit_aggregates, total_credits

        credit_aggregates = self.credit_repo.load_credits(credit_search_query)
        if credit_search_query.should_send_approval_document_url:
            credit_aggregates = self._generate_approval_document_signed_url_for_credits(
                credit_aggregates
            )
        if credit_search_query.should_send_credit_transaction_details:
            credit_aggregates = self.credit_reversal_service.fetch_and_populate_credit_transaction_details(
                credit_aggregates
            )

        total_credits = (
            self.credit_repo.count_credits(credit_search_query)
            if get_total_count
            else None
        )
        return credit_aggregates, total_credits

    @unit_of_work
    def cancel_credit(self, credit_id, cancellation_reason, user_data=None):
        RuleEngine.action_allowed(
            action="cancel_credit",
            facts=CancelCreditFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )

        credit_aggregate = self.credit_repo.load_for_update(credit_id)
        if not credit_aggregate:
            raise ValidationError("Invalid Credit_id")
        validate_credit_type(credit_aggregate.credit.credit_type, CreditType.PAYMENT)
        validate_payment_mode_based_on_role(
            credit_aggregate.credit.mode_of_credit, user_data
        )
        if credit_aggregate.credit.refunded_amount.amount > 0:
            raise ValidationError("Cannot cancel a credit which have refund in it")
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtor_aggregate = self.debtor_repo.load(
            debtor_id=credit_aggregate.debtor_id,
            hotel_id=user_data.hotel_id,
            is_hotel_level_ar_configured=is_hotel_level_ar_configured,
        )
        if credit_aggregate.credit.status == CreditStatus.CANCELLED:
            raise ValidationError("Credit is already cancelled")
        if credit_aggregate.credit.used_to_auto_settle_debit:
            raise ValidationError(
                "Credit is used to auto settle debit, hence cannot be cancelled"
            )

        if credit_aggregate.credit.linked_debit_id:
            self._handle_cancellation_of_linked_debit(credit_aggregate)

        debit_aggregates = None
        related_settlements = self.settlement_repo.load_settlements(credit_id=credit_id)
        if related_settlements:
            debit_aggregates = self.debit_repo.load_all_for_update(
                debit_ids=[settlement.debit_id for settlement in related_settlements]
            )
            for debit_aggregate in debit_aggregates:
                settlement = (
                    debit_aggregate.reverse_settlement_associated_with_given_payment(
                        credit_id
                    )
                )
                self.audit_trail_service.create_settlement_audit_trail(
                    debtor_aggregate,
                    debit_aggregate,
                    settlement,
                    credit_aggregate,
                    debtor_aggregate.hotel_id,
                    AuditType.SETTLEMENT_CANCELLED,
                    user_data,
                )
            self.debit_repo.update_all(debit_aggregates)
        credit_aggregate.cancel(cancellation_reason=cancellation_reason)
        self.credit_repo.update(credit_aggregate)
        self.audit_trail_service.create_credit_audit_trail(
            debtor_aggregate,
            credit_aggregate,
            debtor_aggregate.hotel_id,
            AuditType.CREDIT_CANCELLED,
            debit_aggregates=debit_aggregates,
            user_data=user_data,
        )

        # Emit credit cancelled event
        try:
            cancellation_data = {
                "credit_amount": float(credit_aggregate.credit.amount.value),
                "currency": credit_aggregate.credit.amount.currency,
                "debtor_id": credit_aggregate.credit.debtor_id,
                "cancellation_reason": cancellation_reason,
                "original_reference_number": credit_aggregate.credit.reference_number,
                "cancelled_at": credit_aggregate.credit.updated_at.isoformat() if credit_aggregate.credit.updated_at else None
            }

            self.event_service.emit_credit_cancelled(
                credit_id=credit_aggregate.credit.id,
                recorded_via=EventSource.API,
                cancellation_data=cancellation_data,
                tenant_id=self.tenant_id,
                user_id=user_data.user_id if user_data else None,
                hotel_id=debtor_aggregate.hotel_id
            )
        except Exception as e:
            logger.warning(f"Failed to emit credit cancelled event for credit {credit_aggregate.credit.id}: {e}")

    def _handle_cancellation_of_linked_debit(self, credit_aggregate):
        linked_debit_aggregate = self.debit_repo.load_for_update(
            credit_aggregate.credit.linked_debit_id
        )
        if linked_debit_aggregate.has_settlement():
            raise ValidationError(
                "Debit Associated with this credit has settlement in corresponding debtor ledger"
            )
        linked_debit_aggregate.cancel()
        self.debit_repo.update(linked_debit_aggregate)

    def _create_audit_for_settlement(
        self,
        debit_aggregates,
        credit_aggregate,
        debtor_aggregate,
        debit_id_to_new_settlements_mapping,
        user_data,
    ):
        debit_id_to_debit_aggregate_map = {
            debit_aggregate.debit.debit_id: debit_aggregate
            for debit_aggregate in debit_aggregates
        }
        for debit_id, settlements in debit_id_to_new_settlements_mapping.items():
            debit_aggregate = debit_id_to_debit_aggregate_map.get(debit_id)
            for settlement in settlements:
                self.audit_trail_service.create_settlement_audit_trail(
                    debtor_aggregate,
                    debit_aggregate,
                    settlement,
                    credit_aggregate,
                    debtor_aggregate.hotel_id,
                    AuditType.SETTLEMENT_CREATED,
                    user_data,
                )

    def record_auto_debit_to_corresponding_payment_mode_debtor_if_needed(
        self, credit_aggregate, debtor_configs
    ):
        if not debtor_configs:
            return
        payment_mode_to_debtor_mapping = {
            config.payment_mode: config for config in debtor_configs.values()
        }
        payment_mode_settlement_freq_mapping = {
            config.payment_mode: config.settlement_frequency
            for config in debtor_configs.values()
        }
        debtor_config = payment_mode_to_debtor_mapping.get(
            credit_aggregate.credit.mode_of_credit
        )
        if debtor_config and debtor_config.create_auto_debit:
            debtor_codes = list(debtor_configs.keys())
            debtor_aggregates = self.debtor_repo.search_debtors(
                DebtorSearchQuery(debtor_codes=debtor_codes)
            )
            debtor_code_debtor_id_mapping = {
                debtor_aggregate.debtor.debtor_code: debtor_aggregate.debtor.debtor_id
                for debtor_aggregate in debtor_aggregates
            }
            settlement_freq = payment_mode_settlement_freq_mapping.get(
                credit_aggregate.credit.mode_of_credit, "monthly"
            )
            amount = credit_aggregate.credit.amount_in_base_currency
            debit_amount = Amount(
                pretax_amount=amount,
                tax_amount=Money(0, amount.currency.value),
                posttax_amount=amount,
            )
            debit_data = dict(
                debtor_id=debtor_code_debtor_id_mapping.get(debtor_config.debtor_code),
                debit_date=credit_aggregate.credit.date,
                due_date=calculate_due_date_based_on_settlement_frequency(
                    credit_aggregate.credit.date, settlement_freq
                ),
                debit_amount=debit_amount,
                reference_number=credit_aggregate.credit.reference_number,
                debit_type=DebitType.PAYMENT,
                tenant_id=credit_aggregate.tenant_id,
            )
            debit_aggregate = DebitFactory.create_new_debit(debit_data=debit_data)
            self.debit_repo.save(debit_aggregate)
            credit_aggregate.link_debit(debit_aggregate.debit_id)

    def _get_debtors(
        self,
        debtor_id,
        is_hotel_level_ar_configured,
        user_data,
    ):
        debtors = None
        if (
            (user_data.user_auth_id and user_data.user_type == UserType.FINANCE_POC)
            or is_hotel_level_ar_configured
            or debtor_id
        ):
            debtors, _ = self.debtor_service.get_debtors(
                DebtorSearchQuery(
                    debtor_id=debtor_id,
                    hotel_id=user_data.hotel_id,
                ),
                user_data=user_data,
                hotel_level_accounts_receivable=is_hotel_level_ar_configured,
            )
        return debtors

    @staticmethod
    def _generate_approval_document_signed_url_for_credits(credit_aggregates):
        for credit_aggregate in credit_aggregates:
            if credit_aggregate.credit.approval_document:
                credit_aggregate.credit.approval_document = (
                    AwsServiceClient.get_presigned_url_from_s3_url(
                        credit_aggregate.credit.approval_document, 3600
                    )
                )
        return credit_aggregates

    @unit_of_work
    def create_new_credit_reversal(
        self, credit_reversal_dto: CreditReversalDto, user_data=None
    ):
        RuleEngine.action_allowed(
            action="create_credit_reversal",
            facts=CreateCreditReversalFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtor_aggregate = self.debtor_repo.load(
            credit_reversal_dto.debtor_id,
            user_data.hotel_id,
            is_hotel_level_ar_configured,
        )
        validate_credit_date(credit_reversal_dto.date)
        validate_refund_mode(
            credit_reversal_dto.mode_of_credit,
            debtor_aggregate.debtor_code,
            user_data=user_data,
        )

        self.credit_reversal_service.validate_credit_reversals(credit_reversal_dto)
        self.credit_reversal_service.reset_settlements_and_unpaid_amount(
            credit_reversal_dto.credit_reversals, debtor_aggregate, user_data
        )
        credit_aggregate = CreditReversalFactory.create_new_credit_reversal(
            credit_reversal_dto=credit_reversal_dto
        )

        self.credit_repo.save(credit_aggregate)
        self.credit_reversal_repo.save_all(credit_aggregate)

        # extra details about credit_reversal are required for audit's.
        credit_aggregate = (
            self.credit_reversal_service.fetch_and_populate_credit_transaction_details(
                [credit_aggregate]
            )[0]
        )
        self.audit_trail_service.create_credit_reversal_audit_trail(
            credit_aggregate,
            debtor_aggregate,
            debtor_aggregate.hotel_id,
            user_data=user_data,
        )

        # Emit credit reversal created event
        try:
            # Get the original credit ID from the first reversal (assuming there's at least one)
            original_credit_id = None
            if credit_reversal_dto.credit_reversals:
                original_credit_id = credit_reversal_dto.credit_reversals[0].payment_credit_id

            reversal_data = {
                "reversal_amount": float(credit_aggregate.credit.amount.value),
                "currency": credit_aggregate.credit.amount.currency,
                "debtor_id": credit_aggregate.credit.debtor_id,
                "reversal_date": credit_aggregate.credit.date.isoformat(),
                "reference_number": credit_aggregate.credit.reference_number,
                "description": credit_aggregate.credit.description or "",
                "mode_of_credit": credit_aggregate.credit.mode_of_credit,
                "original_credit_id": original_credit_id,
                "reversal_count": len(credit_reversal_dto.credit_reversals) if credit_reversal_dto.credit_reversals else 0
            }

            self.event_service.emit_credit_reversal(
                credit_reversal_id=credit_aggregate.credit.id,
                credit_id=original_credit_id or "unknown",
                recorded_via=EventSource.API,
                reversal_data=reversal_data,
                tenant_id=self.tenant_id,
                user_id=user_data.user_id if user_data else None,
                hotel_id=debtor_aggregate.hotel_id
            )
        except Exception as e:
            logger.warning(f"Failed to emit credit reversal event for credit {credit_aggregate.credit.id}: {e}")

        return credit_aggregate

    @unit_of_work
    def cancel_credit_reversal(self, credit_id, user_data=None):
        RuleEngine.action_allowed(
            action="cancel_credit_reversal",
            facts=CancelCreditFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )

        refund_credit_aggregate = self.credit_repo.load_for_update(credit_id)
        if not refund_credit_aggregate:
            raise ValidationError("Invalid Credit ID")
        if refund_credit_aggregate.credit.status == CreditStatus.CANCELLED:
            raise ValidationError("Credit is already cancelled")

        validate_credit_type(
            refund_credit_aggregate.credit.credit_type, CreditType.CREDIT_REVERSAL
        )
        validate_payment_mode_based_on_role(
            refund_credit_aggregate.credit.mode_of_credit, user_data
        )

        refund_credit_aggregate = (
            self.credit_reversal_service.fetch_and_populate_credit_transaction_details(
                [refund_credit_aggregate]
            )[0]
        )

        payment_credit_aggregates = (
            self.credit_reversal_service.update_unused_and_refunded_amount(
                refund_credit_aggregate
            )
        )
        debtor_aggregate = self.debtor_repo.load(
            refund_credit_aggregate.credit.debtor_id, user_data.hotel_id, False
        )

        refund_credit_aggregate.cancel()
        self.credit_repo.update_all(payment_credit_aggregates)
        self.credit_repo.update(refund_credit_aggregate)
        self.credit_reversal_repo.update_all(refund_credit_aggregate)

        self.audit_trail_service.cancel_credit_reversal_audit_trail(
            refund_credit_aggregate,
            debtor_aggregate,
            debtor_aggregate.hotel_id,
            user_data=user_data,
        )

        # Emit credit reversal cancelled event
        try:
            # Get the original credit ID from the reversal details
            original_credit_id = None
            if hasattr(refund_credit_aggregate, 'credit_reversal_details') and refund_credit_aggregate.credit_reversal_details:
                original_credit_id = refund_credit_aggregate.credit_reversal_details[0].payment_credit_id

            cancellation_data = {
                "reversal_amount": float(refund_credit_aggregate.credit.amount.value),
                "currency": refund_credit_aggregate.credit.amount.currency,
                "debtor_id": refund_credit_aggregate.credit.debtor_id,
                "original_reference_number": refund_credit_aggregate.credit.reference_number,
                "cancelled_at": refund_credit_aggregate.credit.updated_at.isoformat() if refund_credit_aggregate.credit.updated_at else None,
                "original_credit_id": original_credit_id
            }

            self.event_service.emit_credit_reversal_cancelled(
                credit_reversal_id=refund_credit_aggregate.credit.id,
                credit_id=original_credit_id or "unknown",
                recorded_via=EventSource.API,
                cancellation_data=cancellation_data,
                tenant_id=self.tenant_id,
                user_id=user_data.user_id if user_data else None,
                hotel_id=debtor_aggregate.hotel_id
            )
        except Exception as e:
            logger.warning(f"Failed to emit credit reversal cancelled event for credit {refund_credit_aggregate.credit.id}: {e}")
