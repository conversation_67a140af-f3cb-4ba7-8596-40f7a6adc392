import logging

from marshmallow import ValidationError
from treebo_commons.utils import dateutils

from ar_module.application.hotel_settings.tenant_settings import TenantSettings
from ar_module.application.services.audit_trail_application_service import (
    AuditTrailService,
)
from ar_module.application.services.ar_event_publisher_service import (
    AREventPublisherService,
)
from ar_module.application.services.debtor_service import DebtorService
from ar_module.core.common.globals import global_context
from ar_module.domain.constants import AuditType, UserType
from ar_module.domain.dtos.debtor_search_query import DebtorSearchQuery
from ar_module.domain.factories.debit_factory import DebitFactory
from ar_module.domain.policy.engine import RuleEngine
from ar_module.domain.policy.errors import PolicyError
from ar_module.domain.policy.facts.create_manual_debit_facts import (
    CreateManualDebitFacts,
)
from ar_module.exceptions import PolicyAuthException
from ar_module.infrastructure.database.repositories.debit.debit_repository import (
    DebitRepository,
)
from ar_module.infrastructure.database.repositories.debtor.debtor_repository import (
    DebtorRepository,
)
from ar_module.infrastructure.database.repositories.user.user_repository import (
    UserRepository,
)
from ar_module.infrastructure.db_transaction import unit_of_work
from ar_module.infrastructure.external_clients.aws_service_client import (
    AwsServiceClient,
)
from ar_module.infrastructure.external_clients.core.catalog_service_client import (
    CatalogServiceClient,
)
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(
    dependencies=[
        DebitRepository,
        CatalogServiceClient,
        AuditTrailService,
        DebtorService,
        TenantSettings,
        UserRepository,
AREventPublisherService
    ]
)
class DebitService(object):
    def __init__(
        self,
        debit_repo: DebitRepository,
        catalog_client: CatalogServiceClient,
        audit_trail_service: AuditTrailService,
        debtor_service: DebtorService,
        tenant_settings: TenantSettings,
        user_repo: UserRepository,
        ar_event_publisher_service: AREventPublisherService,
    ):
        self.debit_repo = debit_repo
        self.catalog_client = catalog_client
        self.audit_trail_service = audit_trail_service
        self.debtor_service = debtor_service
        self.tenant_settings = tenant_settings
        self.user_repo = user_repo
        self.ar_event_publisher_service = ar_event_publisher_service

    @unit_of_work
    def create_new_debit(self, debit_data, user_data=None):
        RuleEngine.action_allowed(
            action="create_manual_debit",
            facts=CreateManualDebitFacts(
                user_type=user_data.user_type
                if user_data
                else global_context.get_user_type()
            ),
            fail_on_error=True,
        )
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtors = self._get_debtors(
            debit_data.get("debtor_id"), is_hotel_level_ar_configured, user_data
        )
        if not debtors:
            raise PolicyAuthException(
                error=PolicyError.CREATE_MANUAL_DEBIT_NOT_ALLOWED,
                description=f"Don't have access to debtor {debit_data.get('debtor_id')}",
            )
        if user_data.hotel_id:
            self.validate_debit_date(
                debit_data.get("debit_date"), hotel_id=user_data.hotel_id
            )
        debtor_aggregate = debtors[0]
        debit_aggregate = DebitFactory.create_new_debit(
            debit_data=debit_data, debtor_data=debtor_aggregate.debtor
        )
        self.debit_repo.save(debit_aggregate)

        self.audit_trail_service.create_debit_audit_trail(
            debit_aggregate,
            debtor_aggregate,
            debtor_aggregate.hotel_id,
            AuditType.DEBIT_CREATED,
            user_data=user_data,
        )
        debit_data = DebitPublisherSchema().dump(debit_aggregate.debit).data
        self.ar_event_publisher_service.publish_debit_created(
            entity_id=debit_aggregate.debit_id,
            body=debit_data,
        )
        return debit_aggregate

    def get_settlements(self, debit_id, user_data):
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtor_id_associated_with_debit = self.debit_repo.load(debit_id).debit.debtor_id
        debtors = self._get_debtors(
            debtor_id_associated_with_debit, is_hotel_level_ar_configured, user_data
        )
        if not debtors:
            raise PolicyAuthException(
                error=PolicyError.VIEW_SETTLEMENTS_NOT_ALLOWED,
                description=f"Don't have access to debtor {debtor_id_associated_with_debit}",
            )
        debit_aggregate = self.debit_repo.load(debit_id)
        return debit_aggregate.settlements

    def search_debits(self, debit_search_query, get_total_count=False, user_data=None):
        debit_aggregates, total_debits = [], 0
        is_hotel_level_ar_configured = (
            self.tenant_settings.is_hotel_level_accounts_receivable_configured()
        )
        debtors = self._get_debtors(
            debit_search_query.debtor_id,
            is_hotel_level_ar_configured,
            user_data,
            debit_search_query,
        )
        debit_search_query.debtor_ids = (
            sorted(d.debtor_id for d in debtors) if debtors else None
        )

        if debit_search_query.debtor_id and not debtors:
            return debit_aggregates, total_debits

        debit_aggregates = self.debit_repo.search_debits(debit_search_query)
        debit_aggregates = self._refresh_debit_template_signed_url_for_debits(
            debit_aggregates
        )
        total_debits = (
            self.debit_repo.count_debits(debit_search_query)
            if get_total_count
            else None
        )
        return debit_aggregates, total_debits

    def _get_debtors(
        self,
        debtor_id,
        is_hotel_level_ar_configured,
        user_data,
        debit_search_query=None,
    ):
        debtors = None
        if (
            (user_data.user_auth_id and user_data.user_type == UserType.FINANCE_POC)
            or is_hotel_level_ar_configured
            or debtor_id
        ):
            debtors, _ = self.debtor_service.get_debtors(
                DebtorSearchQuery(
                    debtor_id=debtor_id,
                    hotel_id=user_data.hotel_id
                    or (debit_search_query.hotel_id if debit_search_query else None),
                ),
                user_data=user_data,
                hotel_level_accounts_receivable=is_hotel_level_ar_configured,
            )
        return debtors

    def validate_debit_date(self, debit_date, hotel_id):
        hotel_details = self.catalog_client.fetch_property(hotel_id)
        current_business_date = hotel_details.get("current_business_date")
        if debit_date > dateutils.ymd_str_to_date(current_business_date):
            raise ValidationError(
                "Debit date should be less than or equal to current business date"
            )

    @staticmethod
    def _refresh_debit_template_signed_url_for_debits(debit_aggregates):
        for debit_aggregate in debit_aggregates:
            if debit_aggregate.debit.debit_template_url:
                debit_aggregate.debit.debit_template_url = (
                    AwsServiceClient.get_presigned_url_from_s3_url(
                        debit_aggregate.debit.debit_template_url, 3600
                    )
                )
        return debit_aggregates
