import logging
from typing import Any, Dict

from ar_module.domain.events.dtos.event_publisher_dto import AREventPublisherDTO
from ar_module.domain.events.event_publisher_factory import ARIntegrationEventFactory
from ar_module.infrastructure.events.publisher import AREventPublisher
from object_registry import register_instance

logger = logging.getLogger(__name__)


@register_instance(dependencies=[AREventPublisher])
class AREventPublisherService:
    def __init__(self, event_publisher: AREventPublisher):
        self.event_publisher = event_publisher

    def publish_event(self, event_dto: AREventPublisherDTO) -> bool:
        try:
            event_entity = ARIntegrationEventFactory.create_integration_event(event_dto)
            self.event_publisher.publish(event_entity)
            logger.info(
                f"Successfully published AR event with event_id {event_entity.event_id}"
            )
            return True

        except Exception as e:
            logger.error(
                f"Failed to publish AR event for entity {event_dto.entity_id}: {e}"
            )
            return False

    def publish_credit_created(
        self,
        entity_id: str,
        body: Dict[str, Any],
    ) -> bool:
        event_dto = ARIntegrationEventFactory.create_credit_created_event_dto(
            entity_id=entity_id,
            body=body,
        )
        return self.publish_event(event_dto)

    def publish_credit_reversal(
        self,
        entity_id: str,
        body: Dict[str, Any],
    ) -> bool:
        """Publish a credit reversed event"""
        event_dto = ARIntegrationEventFactory.create_credit_reversal_event_dto(
            entity_id=entity_id,
            body=body,
        )
        return self.publish_event(event_dto)

    def publish_cancel_credit_reversal(
        self,
        entity_id: str,
        body: Dict[str, Any],
    ) -> bool:
        """Publish a credit reversed event"""
        event_dto = ARIntegrationEventFactory.create_credit_reversal_cancelled_event_dto(
            entity_id=entity_id,
            body=body,
        )
        return self.publish_event(event_dto)

    def publish_debit_created(
        self,
        entity_id: str,
        body: Dict[str, Any],
    ) -> bool:
        event_dto = ARIntegrationEventFactory.create_debit_created_event_dto(
            entity_id=entity_id,
            body=body,
        )
        return self.publish_event(event_dto)
