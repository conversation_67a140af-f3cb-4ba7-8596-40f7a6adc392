# coding=utf-8
"""
Unit tests for AR Integration Events (Prometheus pattern)
"""
from datetime import datetime
from unittest import TestCase
from unittest.mock import Mock, patch

from ar_module.domain.events.dtos.event_publisher_dto import (
    ARIntegrationEventType,
    AREventPublisherDTO,
    create_credit_created_event_dto,
    create_debit_created_event_dto
)
from ar_module.domain.events.event_publisher_factory import ARIntegrationEventFactory
from ar_module.application.services.ar_event_publisher_service import AREventPublisherService


class TestARIntegrationEventDTO(TestCase):
    """Test cases for AR Integration Event DTO"""
    
    def test_create_credit_created_event_dto(self):
        """Test creating a credit created event DTO"""
        credit_data = {
            "credit_amount": 1000.0,
            "currency": "INR",
            "debtor_id": "debtor-123"
        }
        
        event_dto = create_credit_created_event_dto(
            credit_id="credit-123",
            recorded_via="api",
            credit_data=credit_data,
            tenant_id="tenant-456",
            user_id="user-789"
        )
        
        self.assertEqual(event_dto.event_type, ARIntegrationEventType.CREDIT_CREATED)
        self.assertEqual(event_dto.entity_id, "credit-123")
        self.assertEqual(event_dto.entity_type, "credit")
        self.assertEqual(event_dto.user_action, "create")
        self.assertEqual(event_dto.recorded_via, "api")
        self.assertEqual(event_dto.body, credit_data)
        self.assertEqual(event_dto.tenant_id, "tenant-456")
        self.assertEqual(event_dto.user_id, "user-789")
    
    def test_create_debit_created_event_dto(self):
        """Test creating a debit created event DTO"""
        debit_data = {
            "debit_amount": 5000.0,
            "currency": "INR",
            "debtor_id": "debtor-123"
        }
        
        event_dto = create_debit_created_event_dto(
            debit_id="debit-456",
            recorded_via="bulk_upload",
            debit_data=debit_data,
            hotel_id="hotel-789"
        )
        
        self.assertEqual(event_dto.event_type, ARIntegrationEventType.DEBIT_CREATED)
        self.assertEqual(event_dto.entity_id, "debit-456")
        self.assertEqual(event_dto.entity_type, "debit")
        self.assertEqual(event_dto.user_action, "create")
        self.assertEqual(event_dto.recorded_via, "bulk_upload")
        self.assertEqual(event_dto.body, debit_data)
        self.assertEqual(event_dto.hotel_id, "hotel-789")
    
    def test_event_type_routing_key(self):
        """Test event type routing key generation"""
        self.assertEqual(
            ARIntegrationEventType.CREDIT_CREATED.routing_key,
            "ar.events.credit.created"
        )
        self.assertEqual(
            ARIntegrationEventType.DEBIT_CREATED.routing_key,
            "ar.events.debit.created"
        )


class TestARIntegrationEventFactory(TestCase):
    """Test cases for AR Integration Event Factory"""
    
    @patch('ar_module.domain.events.integration_event_factory.random_id_generator')
    @patch('ar_module.domain.events.integration_event_factory.get_current_tenant_id')
    def test_create_integration_event(self, mock_get_tenant, mock_id_gen):
        """Test creating an integration event entity from DTO"""
        mock_id_gen.return_value = "AR_EVT123"
        mock_get_tenant.return_value = "tenant-456"
        
        event_dto = AREventPublisherDTO(
            event_type=ARIntegrationEventType.CREDIT_CREATED,
            entity_id="credit-123",
            entity_type="credit",
            user_action="create",
            recorded_via="api",
            body={"amount": 1000.0},
            tenant_id="tenant-456",
            user_id="user-789",
            generated_at=datetime(2024, 1, 15, 10, 30, 0)
        )
        
        event_entity = ARIntegrationEventFactory.create_integration_event(event_dto)
        
        # Test entity attributes
        self.assertEqual(event_entity.event_id, "AR_EVT123")
        self.assertEqual(event_entity.event_type, ARIntegrationEventType.CREDIT_CREATED)
        self.assertEqual(event_entity.entity_id, "credit-123")
        self.assertEqual(event_entity.entity_type, "credit")
        self.assertEqual(event_entity.user_action, "create")
        self.assertEqual(event_entity.recorded_via, "api")
        self.assertEqual(event_entity.tenant_id, "tenant-456")
        self.assertEqual(event_entity.user_id, "user-789")
        
        # Test event body structure
        self.assertEqual(event_entity.body["message_id"], "AR_EVT123")
        self.assertEqual(event_entity.body["event_type"], "credit.created")
        self.assertEqual(event_entity.body["entity_id"], "credit-123")
        self.assertEqual(event_entity.body["entity_type"], "credit")
        self.assertEqual(event_entity.body["user_action"], "create")
        self.assertEqual(event_entity.body["recorded_via"], "api")
        self.assertEqual(event_entity.body["data"], {"amount": 1000.0})
        self.assertEqual(event_entity.body["tenant_id"], "tenant-456")
        self.assertEqual(event_entity.body["user_id"], "user-789")
        self.assertIn("generated_at", event_entity.body)


class TestARIntegrationEventService(TestCase):
    """Test cases for AR Integration Event Service"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_publisher = Mock()
        self.event_service = AREventPublisherService(self.mock_publisher)
    
    def test_service_initialization(self):
        """Test service initialization"""
        self.assertEqual(self.event_service.event_publisher, self.mock_publisher)
        self.assertTrue(self.event_service.is_enabled())
    
    def test_enable_disable_events(self):
        """Test enabling and disabling events"""
        self.assertTrue(self.event_service.is_enabled())
        
        self.event_service.disable_events()
        self.assertFalse(self.event_service.is_enabled())
        
        self.event_service.enable_events()
        self.assertTrue(self.event_service.is_enabled())
    
    @patch('ar_module.application.services.ar_integration_event_service.ARIntegrationEventFactory.create_integration_event')
    def test_publish_event_when_enabled(self, mock_factory):
        """Test publishing event when service is enabled"""
        mock_entity = Mock()
        mock_factory.return_value = mock_entity
        
        event_dto = AREventPublisherDTO(
            event_type=ARIntegrationEventType.CREDIT_CREATED,
            entity_id="credit-123",
            entity_type="credit",
            user_action="create",
            recorded_via="api",
            body={"amount": 1000.0}
        )
        
        result = self.event_service.publish_event(event_dto)
        
        self.assertTrue(result)
        mock_factory.assert_called_once_with(event_dto)
        self.mock_publisher.publish.assert_called_once_with(mock_entity)
    
    def test_publish_event_when_disabled(self):
        """Test publishing event when service is disabled"""
        self.event_service.disable_events()
        
        event_dto = AREventPublisherDTO(
            event_type=ARIntegrationEventType.CREDIT_CREATED,
            entity_id="credit-123",
            entity_type="credit",
            user_action="create",
            recorded_via="api",
            body={"amount": 1000.0}
        )
        
        result = self.event_service.publish_event(event_dto)
        
        self.assertTrue(result)  # Returns True but doesn't actually publish
        self.mock_publisher.publish.assert_not_called()
    
    @patch('ar_module.application.services.ar_integration_event_service.ARIntegrationEventFactory.create_integration_event')
    def test_publish_credit_created(self, mock_factory):
        """Test publishing credit created event"""
        mock_entity = Mock()
        mock_factory.return_value = mock_entity
        
        result = self.event_service.publish_credit_created(
            credit_id="credit-123",
            recorded_via="api",
            credit_data={"amount": 1000.0},
            tenant_id="tenant-456"
        )
        
        self.assertTrue(result)
        self.mock_publisher.publish.assert_called_once_with(mock_entity)
    
    @patch('ar_module.application.services.ar_integration_event_service.ARIntegrationEventFactory.create_integration_event')
    def test_publish_debit_created(self, mock_factory):
        """Test publishing debit created event"""
        mock_entity = Mock()
        mock_factory.return_value = mock_entity
        
        result = self.event_service.publish_debit_created(
            debit_id="debit-456",
            recorded_via="api",
            debit_data={"amount": 5000.0},
            hotel_id="hotel-789"
        )
        
        self.assertTrue(result)
        self.mock_publisher.publish.assert_called_once_with(mock_entity)
    
    @patch('ar_module.application.services.ar_integration_event_service.ARIntegrationEventFactory.create_integration_event')
    def test_publish_event_failure(self, mock_factory):
        """Test handling of publish failure"""
        mock_entity = Mock()
        mock_factory.return_value = mock_entity
        self.mock_publisher.publish.side_effect = Exception("Publish failed")
        
        event_dto = AREventPublisherDTO(
            event_type=ARIntegrationEventType.CREDIT_CREATED,
            entity_id="credit-123",
            entity_type="credit",
            user_action="create",
            recorded_via="api",
            body={"amount": 1000.0}
        )
        
        result = self.event_service.publish_event(event_dto)
        
        self.assertFalse(result)
        self.mock_publisher.publish.assert_called_once_with(mock_entity)
