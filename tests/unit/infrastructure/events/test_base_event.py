# coding=utf-8
"""
Unit tests for AR Module Event Framework Base Events
"""
import json
import uuid
from datetime import datetime
from unittest import TestCase
from unittest.mock import patch

from ar_module.domain.events.base_event import (
    BaseEvent,
    AREvent,
    EntityType,
    EventSource,
    UserAction,
    validate_event,
    create_event_metadata
)


class TestBaseEvent(TestCase):
    """Test cases for BaseEvent class"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_entity_id = "test-entity-123"
        self.test_tenant_id = "test-tenant-456"
        self.test_user_id = "test-user-789"
        self.test_hotel_id = "test-hotel-101"
        
    def test_ar_event_creation(self):
        """Test AREvent creation with all attributes"""
        event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id,
            user_id=self.test_user_id,
            hotel_id=self.test_hotel_id,
            data={"amount": 1000.0, "currency": "INR"}
        )
        
        # Test basic attributes
        self.assertEqual(event.entity_type, EntityType.CREDIT)
        self.assertEqual(event.user_action, UserAction.CREATE)
        self.assertEqual(event.recorded_via, EventSource.API)
        self.assertEqual(event.entity_id, self.test_entity_id)
        self.assertEqual(event.tenant_id, self.test_tenant_id)
        self.assertEqual(event.user_id, self.test_user_id)
        self.assertEqual(event.hotel_id, self.test_hotel_id)
        
        # Test generated attributes
        self.assertIsNotNone(event.id)
        self.assertEqual(event.specversion, "1.0")
        self.assertEqual(event.type, "com.treebo.ar.credit.create")
        self.assertEqual(event.source, "ar-module/credit")
        self.assertEqual(event.subject, f"credit/{self.test_entity_id}")
        self.assertEqual(event.datacontenttype, "application/json")
        self.assertIsInstance(event.time, datetime)
        
        # Test data payload
        self.assertEqual(event.data["amount"], 1000.0)
        self.assertEqual(event.data["currency"], "INR")
    
    def test_event_to_dict(self):
        """Test event serialization to dictionary"""
        event = AREvent(
            entity_type=EntityType.DEBIT,
            user_action=UserAction.UPDATE,
            recorded_via=EventSource.BULK_UPLOAD,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id,
            data={"test": "data"}
        )
        
        event_dict = event.to_dict()
        
        # Test required CloudEvents attributes
        self.assertIn("id", event_dict)
        self.assertIn("specversion", event_dict)
        self.assertIn("type", event_dict)
        self.assertIn("source", event_dict)
        self.assertIn("time", event_dict)
        self.assertIn("subject", event_dict)
        self.assertIn("datacontenttype", event_dict)
        
        # Test AR-specific attributes
        self.assertEqual(event_dict["entity_type"], "debit")
        self.assertEqual(event_dict["user_action"], "update")
        self.assertEqual(event_dict["recorded_via"], "bulk_upload")
        self.assertEqual(event_dict["entity_id"], self.test_entity_id)
        self.assertEqual(event_dict["tenant_id"], self.test_tenant_id)
        
        # Test data payload
        self.assertEqual(event_dict["data"]["test"], "data")
    
    def test_event_to_json(self):
        """Test event serialization to JSON"""
        event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id
        )
        
        json_str = event.to_json()
        
        # Test that it's valid JSON
        parsed = json.loads(json_str)
        self.assertIsInstance(parsed, dict)
        
        # Test key attributes are present
        self.assertEqual(parsed["entity_type"], "credit")
        self.assertEqual(parsed["user_action"], "create")
        self.assertEqual(parsed["recorded_via"], "api")
    
    @patch('ar_module.domain.events.base_event.get_current_tenant_id')
    def test_event_with_default_tenant(self, mock_get_tenant):
        """Test event creation with default tenant ID"""
        mock_get_tenant.return_value = "default-tenant"
        
        event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id
        )
        
        self.assertEqual(event.tenant_id, "default-tenant")
    
    def test_event_validation(self):
        """Test event validation"""
        # Valid event
        valid_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id
        )
        
        self.assertTrue(validate_event(valid_event))
        
        # Invalid event (missing required attribute)
        invalid_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id
        )
        # Remove a required attribute
        delattr(invalid_event, 'entity_type')
        
        self.assertFalse(validate_event(invalid_event))
    
    def test_create_event_metadata(self):
        """Test event metadata creation helper"""
        metadata = create_event_metadata(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            custom_field="custom_value"
        )
        
        self.assertEqual(metadata['entity_type'], EntityType.CREDIT)
        self.assertEqual(metadata['user_action'], UserAction.CREATE)
        self.assertEqual(metadata['recorded_via'], EventSource.API)
        self.assertEqual(metadata['entity_id'], self.test_entity_id)
        self.assertEqual(metadata['custom_field'], "custom_value")
    
    def test_event_string_representation(self):
        """Test event string representation"""
        event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id
        )
        
        str_repr = str(event)
        self.assertIn("AREvent", str_repr)
        self.assertIn(event.id, str_repr)
        self.assertIn(event.type, str_repr)
        self.assertIn(self.test_entity_id, str_repr)
    
    def test_event_with_custom_timestamp(self):
        """Test event creation with custom timestamp"""
        custom_time = datetime(2024, 1, 15, 10, 30, 0)
        
        event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id,
            timestamp=custom_time
        )
        
        self.assertEqual(event.time, custom_time)
    
    def test_event_with_custom_id(self):
        """Test event creation with custom event ID"""
        custom_id = str(uuid.uuid4())
        
        event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id=self.test_entity_id,
            tenant_id=self.test_tenant_id,
            event_id=custom_id
        )
        
        self.assertEqual(event.id, custom_id)


class TestEventEnums(TestCase):
    """Test cases for event enums"""
    
    def test_entity_type_enum(self):
        """Test EntityType enum values"""
        self.assertEqual(EntityType.CREDIT.value, "credit")
        self.assertEqual(EntityType.CREDIT_REVERSAL.value, "credit_reversal")
        self.assertEqual(EntityType.DEBIT.value, "debit")
        self.assertEqual(EntityType.DEBTOR.value, "debtor")
        self.assertEqual(EntityType.SETTLEMENT.value, "settlement")
    
    def test_user_action_enum(self):
        """Test UserAction enum values"""
        self.assertEqual(UserAction.CREATE.value, "create")
        self.assertEqual(UserAction.UPDATE.value, "update")
        self.assertEqual(UserAction.DELETE.value, "delete")
        self.assertEqual(UserAction.CANCEL.value, "cancel")
        self.assertEqual(UserAction.REVERSE.value, "reverse")
        self.assertEqual(UserAction.APPROVE.value, "approve")
        self.assertEqual(UserAction.REJECT.value, "reject")
    
    def test_event_source_enum(self):
        """Test EventSource enum values"""
        self.assertEqual(EventSource.API.value, "api")
        self.assertEqual(EventSource.BULK_UPLOAD.value, "bulk_upload")
        self.assertEqual(EventSource.SYSTEM.value, "system")
        self.assertEqual(EventSource.INTEGRATION.value, "integration")
        self.assertEqual(EventSource.MIGRATION.value, "migration")
        self.assertEqual(EventSource.MANUAL.value, "manual")
