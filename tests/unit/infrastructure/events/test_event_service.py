# coding=utf-8
"""
Unit tests for AR Module Event Service
"""
from unittest import TestCase
from unittest.mock import Mock, patch

from ar_module.domain.events.base_event import EventSource
from ar_module.infrastructure.events.event_service import (
    AREventService,
    get_event_service,
    set_event_service,
    emit_credit_created_event,
    emit_credit_reversal_event,
    emit_debit_created_event
)
from ar_module.infrastructure.events.event_publisher import SynchronousEventPublisher
from ar_module.infrastructure.events.event_handler import EventHandlerRegistry


class TestAREventService(TestCase):
    """Test cases for AREventService"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.mock_publisher = Mock()
        self.mock_registry = Mock()
        self.event_service = AREventService(
            event_publisher=self.mock_publisher,
            event_handler_registry=self.mock_registry
        )
        
        self.test_credit_data = {
            "credit_amount": 1000.0,
            "currency": "INR",
            "debtor_id": "debtor-123",
            "payment_mode": "cash",
            "credit_date": "2024-01-15",
            "reference_number": "REF-001"
        }
        
        self.test_debit_data = {
            "debit_amount": 5000.0,
            "currency": "INR",
            "debtor_id": "debtor-123",
            "debit_date": "2024-01-15",
            "due_date": "2024-02-15",
            "invoice_number": "INV-001"
        }
    
    def test_service_initialization(self):
        """Test service initialization"""
        self.assertEqual(self.event_service.event_publisher, self.mock_publisher)
        self.assertEqual(self.event_service.event_handler_registry, self.mock_registry)
        self.assertTrue(self.event_service.is_enabled())
    
    def test_enable_disable_events(self):
        """Test enabling and disabling events"""
        self.assertTrue(self.event_service.is_enabled())
        
        self.event_service.disable_events()
        self.assertFalse(self.event_service.is_enabled())
        
        self.event_service.enable_events()
        self.assertTrue(self.event_service.is_enabled())
    
    def test_emit_event_when_enabled(self):
        """Test emitting event when service is enabled"""
        from ar_module.domain.events.base_event import AREvent, EntityType, UserAction
        
        test_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-credit-123",
            tenant_id="test-tenant-456"
        )
        
        self.mock_publisher.publish.return_value = True
        self.mock_registry.handle_event.return_value = True
        
        result = self.event_service.emit_event(test_event)
        
        self.assertTrue(result)
        self.mock_publisher.publish.assert_called_once_with(test_event)
        self.mock_registry.handle_event.assert_called_once_with(test_event)
    
    def test_emit_event_when_disabled(self):
        """Test emitting event when service is disabled"""
        from ar_module.domain.events.base_event import AREvent, EntityType, UserAction
        
        test_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-credit-123",
            tenant_id="test-tenant-456"
        )
        
        self.event_service.disable_events()
        result = self.event_service.emit_event(test_event)
        
        self.assertTrue(result)  # Returns True but doesn't actually process
        self.mock_publisher.publish.assert_not_called()
        self.mock_registry.handle_event.assert_not_called()
    
    def test_emit_event_publisher_failure(self):
        """Test emitting event when publisher fails"""
        from ar_module.domain.events.base_event import AREvent, EntityType, UserAction
        
        test_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-credit-123",
            tenant_id="test-tenant-456"
        )
        
        self.mock_publisher.publish.return_value = False
        
        result = self.event_service.emit_event(test_event)
        
        self.assertFalse(result)
        self.mock_publisher.publish.assert_called_once_with(test_event)
        self.mock_registry.handle_event.assert_not_called()
    
    def test_emit_credit_created(self):
        """Test emitting credit created event"""
        self.mock_publisher.publish.return_value = True
        self.mock_registry.handle_event.return_value = True
        
        result = self.event_service.emit_credit_created(
            credit_id="credit-123",
            recorded_via=EventSource.API,
            credit_data=self.test_credit_data,
            tenant_id="tenant-456",
            user_id="user-789",
            hotel_id="hotel-101"
        )
        
        self.assertTrue(result)
        self.mock_publisher.publish.assert_called_once()
        
        # Verify the event was created correctly
        published_event = self.mock_publisher.publish.call_args[0][0]
        self.assertEqual(published_event.type, "com.treebo.ar.credit.created")
        self.assertEqual(published_event.entity_id, "credit-123")
        self.assertEqual(published_event.data, self.test_credit_data)
    
    def test_emit_credit_reversal(self):
        """Test emitting credit reversal event"""
        self.mock_publisher.publish.return_value = True
        self.mock_registry.handle_event.return_value = True
        
        reversal_data = {
            "reversal_amount": 500.0,
            "currency": "INR",
            "original_credit_id": "credit-123",
            "reason": "Customer request"
        }
        
        result = self.event_service.emit_credit_reversal(
            credit_reversal_id="reversal-456",
            credit_id="credit-123",
            recorded_via=EventSource.API,
            reversal_data=reversal_data
        )
        
        self.assertTrue(result)
        self.mock_publisher.publish.assert_called_once()
        
        published_event = self.mock_publisher.publish.call_args[0][0]
        self.assertEqual(published_event.type, "com.treebo.ar.credit.reversed")
        self.assertEqual(published_event.entity_id, "reversal-456")
        self.assertEqual(published_event.original_credit_id, "credit-123")
    
    def test_emit_debit_created(self):
        """Test emitting debit created event"""
        self.mock_publisher.publish.return_value = True
        self.mock_registry.handle_event.return_value = True
        
        result = self.event_service.emit_debit_created(
            debit_id="debit-789",
            recorded_via=EventSource.API,
            debit_data=self.test_debit_data
        )
        
        self.assertTrue(result)
        self.mock_publisher.publish.assert_called_once()
        
        published_event = self.mock_publisher.publish.call_args[0][0]
        self.assertEqual(published_event.type, "com.treebo.ar.debit.created")
        self.assertEqual(published_event.entity_id, "debit-789")
        self.assertEqual(published_event.data, self.test_debit_data)
    
    def test_emit_events_batch(self):
        """Test emitting multiple events"""
        from ar_module.domain.events.base_event import AREvent, EntityType, UserAction
        
        events = [
            AREvent(
                entity_type=EntityType.CREDIT,
                user_action=UserAction.CREATE,
                recorded_via=EventSource.API,
                entity_id="credit-1",
                tenant_id="tenant-456"
            ),
            AREvent(
                entity_type=EntityType.DEBIT,
                user_action=UserAction.CREATE,
                recorded_via=EventSource.API,
                entity_id="debit-1",
                tenant_id="tenant-456"
            )
        ]
        
        self.mock_publisher.publish.return_value = True
        self.mock_registry.handle_event.return_value = True
        
        result = self.event_service.emit_events(events)
        
        self.assertTrue(result)
        self.assertEqual(self.mock_publisher.publish.call_count, 2)
        self.assertEqual(self.mock_registry.handle_event.call_count, 2)
    
    def test_emit_events_partial_failure(self):
        """Test emitting events with partial failure"""
        from ar_module.domain.events.base_event import AREvent, EntityType, UserAction
        
        events = [
            AREvent(
                entity_type=EntityType.CREDIT,
                user_action=UserAction.CREATE,
                recorded_via=EventSource.API,
                entity_id="credit-1",
                tenant_id="tenant-456"
            ),
            AREvent(
                entity_type=EntityType.DEBIT,
                user_action=UserAction.CREATE,
                recorded_via=EventSource.API,
                entity_id="debit-1",
                tenant_id="tenant-456"
            )
        ]
        
        # First event succeeds, second fails
        self.mock_publisher.publish.side_effect = [True, False]
        self.mock_registry.handle_event.return_value = True
        
        result = self.event_service.emit_events(events)
        
        self.assertFalse(result)
        self.assertEqual(self.mock_publisher.publish.call_count, 2)
        self.assertEqual(self.mock_registry.handle_event.call_count, 1)  # Only called for successful publish


class TestGlobalEventService(TestCase):
    """Test cases for global event service functions"""
    
    def tearDown(self):
        """Clean up after tests"""
        set_event_service(None)
    
    @patch('ar_module.infrastructure.events.event_service.AREventService')
    def test_get_event_service_default(self, mock_service_class):
        """Test getting default event service"""
        mock_service = Mock()
        mock_service_class.return_value = mock_service
        
        service = get_event_service()
        
        mock_service_class.assert_called_once()
        self.assertEqual(service, mock_service)
    
    def test_set_and_get_event_service(self):
        """Test setting and getting custom event service"""
        custom_service = AREventService()
        
        set_event_service(custom_service)
        service = get_event_service()
        
        self.assertEqual(service, custom_service)
    
    def test_convenience_functions(self):
        """Test convenience functions for emitting events"""
        mock_service = Mock()
        mock_service.emit_credit_created.return_value = True
        mock_service.emit_credit_reversal.return_value = True
        mock_service.emit_debit_created.return_value = True
        
        set_event_service(mock_service)
        
        # Test credit created
        result1 = emit_credit_created_event(
            credit_id="credit-123",
            recorded_via=EventSource.API,
            credit_data={"amount": 1000}
        )
        
        # Test credit reversal
        result2 = emit_credit_reversal_event(
            credit_reversal_id="reversal-456",
            credit_id="credit-123",
            recorded_via=EventSource.API,
            reversal_data={"amount": 500}
        )
        
        # Test debit created
        result3 = emit_debit_created_event(
            debit_id="debit-789",
            recorded_via=EventSource.API,
            debit_data={"amount": 2000}
        )
        
        self.assertTrue(result1)
        self.assertTrue(result2)
        self.assertTrue(result3)
        
        mock_service.emit_credit_created.assert_called_once()
        mock_service.emit_credit_reversal.assert_called_once()
        mock_service.emit_debit_created.assert_called_once()


class TestEventServiceIntegration(TestCase):
    """Integration tests for event service"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sync_publisher = SynchronousEventPublisher()
        self.event_registry = EventHandlerRegistry()
        self.event_service = AREventService(
            event_publisher=self.sync_publisher,
            event_handler_registry=self.event_registry
        )
    
    def test_end_to_end_credit_event_flow(self):
        """Test end-to-end credit event flow"""
        # Set up event handler
        handled_events = []
        
        def test_handler(event):
            handled_events.append(event)
            return True
        
        self.event_registry.register_function_handler(
            test_handler,
            ["com.treebo.ar.credit.created"]
        )
        
        # Emit credit created event
        result = self.event_service.emit_credit_created(
            credit_id="credit-123",
            recorded_via=EventSource.API,
            credit_data={
                "credit_amount": 1000.0,
                "currency": "INR",
                "debtor_id": "debtor-123"
            },
            tenant_id="tenant-456"
        )
        
        # Verify results
        self.assertTrue(result)
        
        # Check publisher
        published_events = self.sync_publisher.get_published_events()
        self.assertEqual(len(published_events), 1)
        
        published_event = published_events[0]
        self.assertEqual(published_event.type, "com.treebo.ar.credit.created")
        self.assertEqual(published_event.entity_id, "credit-123")
        
        # Check handler
        self.assertEqual(len(handled_events), 1)
        self.assertEqual(handled_events[0], published_event)
