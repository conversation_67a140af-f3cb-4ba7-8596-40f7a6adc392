# coding=utf-8
"""
Unit tests for AR Module Event Publisher
"""
from unittest import TestCase
from unittest.mock import Mock, patch, MagicMock

from ar_module.domain.events.base_event import AREvent, EntityType, EventSource, UserAction
from ar_module.infrastructure.events.event_publisher import (
    SynchronousEventPublisher,
    RabbitMQEventPublisher,
    EventPublisherFactory,
    get_event_publisher,
    set_event_publisher,
    publish_event,
    publish_events
)


class TestSynchronousEventPublisher(TestCase):
    """Test cases for SynchronousEventPublisher"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.publisher = SynchronousEventPublisher()
        self.test_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-credit-123",
            tenant_id="test-tenant-456"
        )
    
    def test_publish_single_event(self):
        """Test publishing a single event"""
        result = self.publisher.publish(self.test_event)
        
        self.assertTrue(result)
        self.assertEqual(len(self.publisher.published_events), 1)
        self.assertEqual(self.publisher.published_events[0], self.test_event)
    
    def test_publish_multiple_events(self):
        """Test publishing multiple events"""
        event2 = AREvent(
            entity_type=EntityType.DEBIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-debit-456",
            tenant_id="test-tenant-456"
        )
        
        events = [self.test_event, event2]
        result = self.publisher.publish_batch(events)
        
        self.assertTrue(result)
        self.assertEqual(len(self.publisher.published_events), 2)
    
    def test_get_published_events(self):
        """Test getting published events"""
        self.publisher.publish(self.test_event)
        
        published = self.publisher.get_published_events()
        
        self.assertEqual(len(published), 1)
        self.assertEqual(published[0], self.test_event)
        
        # Ensure it returns a copy
        published.clear()
        self.assertEqual(len(self.publisher.published_events), 1)
    
    def test_clear_published_events(self):
        """Test clearing published events"""
        self.publisher.publish(self.test_event)
        self.assertEqual(len(self.publisher.published_events), 1)
        
        self.publisher.clear_published_events()
        self.assertEqual(len(self.publisher.published_events), 0)
    
    @patch('ar_module.domain.events.base_event.validate_event')
    def test_publish_invalid_event(self, mock_validate):
        """Test publishing an invalid event"""
        mock_validate.return_value = False
        
        result = self.publisher.publish(self.test_event)
        
        self.assertFalse(result)
        self.assertEqual(len(self.publisher.published_events), 0)


class TestRabbitMQEventPublisher(TestCase):
    """Test cases for RabbitMQEventPublisher"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-credit-123",
            tenant_id="test-tenant-456"
        )
    
    @patch('ar_module.infrastructure.events.event_publisher.BaseQueueService.__init__')
    def test_publisher_initialization(self, mock_init):
        """Test RabbitMQ publisher initialization"""
        mock_init.return_value = None
        
        publisher = RabbitMQEventPublisher()
        
        self.assertIsNotNone(publisher)
        self.assertEqual(publisher._tenant_wise_producers, {})
    
    @patch('ar_module.infrastructure.events.event_publisher.validate_event')
    @patch('ar_module.infrastructure.events.event_publisher.get_current_tenant_id')
    def test_publish_with_invalid_event(self, mock_get_tenant, mock_validate):
        """Test publishing with invalid event"""
        mock_validate.return_value = False
        mock_get_tenant.return_value = "test-tenant"
        
        publisher = RabbitMQEventPublisher()
        result = publisher.publish(self.test_event)
        
        self.assertFalse(result)
    
    def test_get_routing_key(self):
        """Test routing key generation"""
        publisher = RabbitMQEventPublisher()
        
        routing_key = publisher._get_routing_key(self.test_event)
        
        self.assertEqual(routing_key, "ar.events.credit.create")
    
    @patch('ar_module.infrastructure.events.event_publisher.validate_event')
    def test_publish_batch_with_mixed_results(self, mock_validate):
        """Test batch publishing with mixed success/failure"""
        # First event succeeds, second fails
        mock_validate.side_effect = [True, False]
        
        event2 = AREvent(
            entity_type=EntityType.DEBIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-debit-456",
            tenant_id="test-tenant-456"
        )
        
        publisher = RabbitMQEventPublisher()
        
        with patch.object(publisher, 'publish', side_effect=[True, False]):
            result = publisher.publish_batch([self.test_event, event2])
        
        self.assertFalse(result)


class TestEventPublisherFactory(TestCase):
    """Test cases for EventPublisherFactory"""
    
    def test_create_rabbitmq_publisher(self):
        """Test creating RabbitMQ publisher"""
        publisher = EventPublisherFactory.create_publisher("rabbitmq")
        
        self.assertIsInstance(publisher, RabbitMQEventPublisher)
    
    def test_create_sync_publisher(self):
        """Test creating synchronous publisher"""
        publisher = EventPublisherFactory.create_publisher("sync")
        
        self.assertIsInstance(publisher, SynchronousEventPublisher)
    
    def test_create_unknown_publisher(self):
        """Test creating unknown publisher type"""
        with self.assertRaises(ValueError):
            EventPublisherFactory.create_publisher("unknown")


class TestGlobalEventPublisher(TestCase):
    """Test cases for global event publisher functions"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.test_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-credit-123",
            tenant_id="test-tenant-456"
        )
    
    def tearDown(self):
        """Clean up after tests"""
        # Reset global publisher
        set_event_publisher(None)
    
    @patch('ar_module.infrastructure.events.event_publisher.EventPublisherFactory.create_publisher')
    def test_get_event_publisher_default(self, mock_create):
        """Test getting default event publisher"""
        mock_publisher = Mock()
        mock_create.return_value = mock_publisher
        
        publisher = get_event_publisher()
        
        mock_create.assert_called_once_with("rabbitmq")
        self.assertEqual(publisher, mock_publisher)
    
    def test_set_and_get_event_publisher(self):
        """Test setting and getting custom event publisher"""
        custom_publisher = SynchronousEventPublisher()
        
        set_event_publisher(custom_publisher)
        publisher = get_event_publisher()
        
        self.assertEqual(publisher, custom_publisher)
    
    def test_publish_event_convenience_function(self):
        """Test publish_event convenience function"""
        mock_publisher = Mock()
        mock_publisher.publish.return_value = True
        set_event_publisher(mock_publisher)
        
        result = publish_event(self.test_event)
        
        self.assertTrue(result)
        mock_publisher.publish.assert_called_once_with(self.test_event)
    
    def test_publish_events_convenience_function(self):
        """Test publish_events convenience function"""
        mock_publisher = Mock()
        mock_publisher.publish_batch.return_value = True
        set_event_publisher(mock_publisher)
        
        events = [self.test_event]
        result = publish_events(events)
        
        self.assertTrue(result)
        mock_publisher.publish_batch.assert_called_once_with(events)


class TestEventPublisherIntegration(TestCase):
    """Integration tests for event publisher"""
    
    def setUp(self):
        """Set up test fixtures"""
        self.sync_publisher = SynchronousEventPublisher()
        self.test_event = AREvent(
            entity_type=EntityType.CREDIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.API,
            entity_id="test-credit-123",
            tenant_id="test-tenant-456",
            data={"amount": 1000.0, "currency": "INR"}
        )
    
    def test_end_to_end_event_publishing(self):
        """Test end-to-end event publishing flow"""
        # Set up synchronous publisher for testing
        set_event_publisher(self.sync_publisher)
        
        # Publish event using convenience function
        result = publish_event(self.test_event)
        
        # Verify results
        self.assertTrue(result)
        published_events = self.sync_publisher.get_published_events()
        self.assertEqual(len(published_events), 1)
        
        published_event = published_events[0]
        self.assertEqual(published_event.entity_type, EntityType.CREDIT)
        self.assertEqual(published_event.user_action, UserAction.CREATE)
        self.assertEqual(published_event.entity_id, "test-credit-123")
        self.assertEqual(published_event.data["amount"], 1000.0)
    
    def test_batch_publishing_flow(self):
        """Test batch publishing flow"""
        event2 = AREvent(
            entity_type=EntityType.DEBIT,
            user_action=UserAction.CREATE,
            recorded_via=EventSource.BULK_UPLOAD,
            entity_id="test-debit-456",
            tenant_id="test-tenant-456"
        )
        
        set_event_publisher(self.sync_publisher)
        
        result = publish_events([self.test_event, event2])
        
        self.assertTrue(result)
        published_events = self.sync_publisher.get_published_events()
        self.assertEqual(len(published_events), 2)
    
    def tearDown(self):
        """Clean up after tests"""
        set_event_publisher(None)
