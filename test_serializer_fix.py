#!/usr/bin/env python3
"""
Test script to verify the PaymentsPushSchema serialization fixes.
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, '/Users/<USER>/Work/accounts_receivable')

from ar_module.reporting.finance_erp_reporting.serializers.finance_erp_reporting import (
    PaymentsPushSchema,
    convert_date_format
)


class MockPaymentAggregate:
    """Mock object to simulate PaymentReportAggregate"""
    def __init__(self):
        self.posting_date = "25-12-2023"  # DD-MM-YYYY format
        self.payment_date = "15-03-2024"  # DD-MM-YYYY format
        self.check_in = "01-01-2024"      # DD-MM-YYYY format
        self.check_out = "05-01-2024"     # DD-MM-YYYY format
        self.pg_transaction_id = "TXN123456"
        self.hotel_code = "HTL001"


def test_date_conversion():
    """Test the convert_date_format function"""
    print("Testing convert_date_format function:")
    
    # Test valid date conversion
    result = convert_date_format("25-12-2023")
    expected = "2023-12-25"
    print(f"Input: '25-12-2023' -> Output: '{result}' (Expected: '{expected}')")
    assert result == expected, f"Expected {expected}, got {result}"
    
    # Test None input
    result = convert_date_format(None)
    print(f"Input: None -> Output: {result} (Expected: None)")
    assert result is None, f"Expected None, got {result}"
    
    # Test invalid date format
    result = convert_date_format("invalid-date")
    print(f"Input: 'invalid-date' -> Output: {result} (Expected: None)")
    assert result is None, f"Expected None, got {result}"
    
    print("✅ convert_date_format tests passed!\n")


def test_schema_serialization():
    """Test the PaymentsPushSchema serialization"""
    print("Testing PaymentsPushSchema serialization:")
    
    # Create mock data
    mock_aggregate = MockPaymentAggregate()
    
    # Create schema instance
    schema = PaymentsPushSchema()
    
    # Serialize the data
    result = schema.dump(mock_aggregate)
    
    print("Serialized data:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    # Check date formats in output
    expected_dates = {
        "PostingDate": "2023-12-25",
        "PaymentDate": "2024-03-15", 
        "CheckInDate": "2024-01-01",
        "CheckOutDate": "2024-01-05"
    }
    
    print("\nDate format verification:")
    for field, expected in expected_dates.items():
        actual = result.get(field)
        print(f"  {field}: '{actual}' (Expected: '{expected}')")
        assert actual == expected, f"Field {field}: expected {expected}, got {actual}"
    
    print("✅ Schema serialization tests passed!\n")


def test_schema_with_none_dates():
    """Test schema with None date values"""
    print("Testing PaymentsPushSchema with None dates:")
    
    # Create mock data with None dates
    class MockAggregateWithNones:
        def __init__(self):
            self.posting_date = None
            self.payment_date = "15-03-2024"
            self.check_in = None
            self.check_out = None
            self.pg_transaction_id = "TXN123456"
    
    mock_aggregate = MockAggregateWithNones()
    schema = PaymentsPushSchema()
    result = schema.dump(mock_aggregate)
    
    print("Serialized data with None dates:")
    for key, value in result.items():
        print(f"  {key}: {value}")
    
    # Verify None values are handled correctly
    assert result.get("PostingDate") is None
    assert result.get("PaymentDate") == "2024-03-15"
    assert result.get("CheckInDate") is None
    assert result.get("CheckOutDate") is None
    
    print("✅ None date handling tests passed!\n")


if __name__ == "__main__":
    print("Running PaymentsPushSchema serialization tests...\n")
    
    try:
        test_date_conversion()
        test_schema_serialization()
        test_schema_with_none_dates()
        
        print("🎉 All tests passed! The serialization fixes are working correctly.")
        print("\nSummary:")
        print("- Date format conversion: DD-MM-YYYY → YYYY-MM-DD ✅")
        print("- Schema uses 'serialize' parameter correctly ✅") 
        print("- None values handled gracefully ✅")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        sys.exit(1)
