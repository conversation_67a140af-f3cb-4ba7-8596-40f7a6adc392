# AR Integration Events - Simplified Prometheus Pattern

## Overview

This is a simplified AR integration event framework following the Prometheus pattern. It focuses on **just publishing events** without complex infrastructure, tables, or models.

## Key Features

- **Simple & Clean**: Following Prometheus pattern - no database tables, just publish events
- **Direct Publishing**: Events are published directly to RabbitMQ
- **Minimal Dependencies**: Lightweight implementation with minimal overhead
- **Easy Integration**: Simple service integration with existing AR services

## Architecture

```
AR Service → Event DTO → Event Factory → Event Entity → Publisher → RabbitMQ
```

## Event Types

| Event Type | Routing Key | Description |
|------------|-------------|-------------|
| `credit.created` | `ar.events.credit.created` | Credit creation |
| `credit.cancelled` | `ar.events.credit.cancelled` | Credit cancellation |
| `credit.reversed` | `ar.events.credit.reversed` | Credit reversal |
| `credit.reversal.cancelled` | `ar.events.credit.reversal.cancelled` | Credit reversal cancellation |
| `debit.created` | `ar.events.debit.created` | Debit creation |
| `debit.updated` | `ar.events.debit.updated` | Debit update |
| `debit.cancelled` | `ar.events.debit.cancelled` | Debit cancellation |

## Usage Examples

### 1. Basic Event Publishing

```python
from ar_module.application.services.ar_event_publisher_service import (
   publish_credit_created_event,
   publish_debit_created_event
)

# Publish credit created event
success = publish_credit_created_event(
   credit_id="CR-2024-001",
   recorded_via="api",
   credit_data={
      "credit_amount": 1000.0,
      "currency": "INR",
      "debtor_id": "debtor-123",
      "payment_mode": "cash"
   },
   tenant_id="tenant-456",
   user_id="user-789",
   hotel_id="hotel-101"
)

# Publish debit created event
success = publish_debit_created_event(
   debit_id="DB-2024-001",
   recorded_via="api",
   debit_data={
      "debit_amount": 5000.0,
      "currency": "INR",
      "debtor_id": "debtor-123"
   },
   hotel_id="hotel-101"
)
```

### 2. Service Integration

```python
from ar_module.application.services.ar_event_publisher_service import AREventPublisherService


class CreditService:
   def __init__(self, ..., event_service: AREventPublisherService):
      self.event_service = event_service

   def create_credit(self, credit_data):
      # Create credit logic
      credit = self._create_credit_logic(credit_data)

      # Publish event
      try:
         self.event_service.publish_credit_created(
            credit_id=credit.credit_id,
            recorded_via="api",
            credit_data={
               "credit_amount": float(credit.amount_in_base_currency.value),
               "currency": credit.amount_in_base_currency.currency.value,
               "debtor_id": credit.debtor_id,
               "payment_mode": credit.mode_of_credit
            },
            tenant_id=self.tenant_id,
            user_id=user_data.user_id if user_data else None,
            hotel_id=debtor_aggregate.hotel_id
         )
      except Exception as e:
         logger.warning(f"Failed to publish credit created event: {e}")

      return credit
```

### 3. Custom Event Creation

```python
from ar_module.domain.events.dtos.event_publisher_dto import (
   AREventPublisherDTO,
   ARIntegrationEventType
)
from ar_module.application.services.ar_event_publisher_service import get_ar_integration_event_service

# Create custom event DTO
event_dto = AREventPublisherDTO(
   event_type=ARIntegrationEventType.CREDIT_CREATED,
   entity_id="credit-123",
   entity_type="credit",
   user_action="create",
   recorded_via="api",
   body={"custom": "data"},
   tenant_id="tenant-456"
)

# Publish the event
service = get_ar_integration_event_service()
success = service.publish_event(event_dto)
```

## Event Payload Structure

Events follow this structure:

```json
{
  "message_id": "AR_EVT123456",
  "generated_at": "2024-01-15T10:30:00Z",
  "event_type": "credit.created",
  "entity_id": "CR-2024-001",
  "entity_type": "credit",
  "user_action": "create",
  "recorded_via": "api",
  "tenant_id": "tenant-456",
  "user_id": "user-789",
  "hotel_id": "hotel-101",
  "data": {
    "credit_amount": 1000.0,
    "currency": "INR",
    "debtor_id": "debtor-123",
    "payment_mode": "cash"
  }
}
```

## Configuration

### RabbitMQ Setup

- **Exchange**: `ar-integration-events`
- **Exchange Type**: `topic`
- **Routing Pattern**: `ar.events.{event_type}`

### Headers

Events include these headers for routing:
- `hotel_id`: Hotel identifier
- `tenant_id`: Tenant identifier
- `entity_type`: Type of entity
- `entity_id`: Entity identifier
- `user_action`: Action performed

## Testing

### Unit Tests

```python
from ar_module.application.services.ar_event_publisher_service import AREventPublisherService
from unittest.mock import Mock

# Setup
mock_publisher = Mock()
service = AREventPublisherService(mock_publisher)

# Test
success = service.publish_credit_created(
   credit_id="test-credit",
   recorded_via="api",
   credit_data={"amount": 1000}
)

# Verify
assert success
mock_publisher.publish.assert_called_once()
```

### Integration Testing

```python
# Disable events for testing
service = get_ar_integration_event_service()
service.disable_events()

# Your test logic here
# Events won't be published

# Re-enable if needed
service.enable_events()
```

## Migration from Complex Framework

If migrating from the complex event framework:

1. **Replace imports**:
   ```python
   # Old
   from ar_module.infrastructure.events.event_service import AREventService
   
   # New
   from ar_module.application.services.ar_event_publisher_service import AREventPublisherService
   ```

2. **Update method calls**:
   ```python
   # Old
   self.event_service.emit_credit_created(...)
   
   # New
   self.event_service.publish_credit_created(...)
   ```

3. **Simplify data structure**:
   ```python
   # Old - Complex event objects
   event = CreditCreatedEvent(...)
   
   # New - Simple data dictionaries
   credit_data = {"credit_amount": 1000.0, "currency": "INR"}
   ```

## Benefits

1. **Simplicity**: No complex event classes, handlers, or database tables
2. **Performance**: Direct publishing without overhead
3. **Maintainability**: Easy to understand and modify
4. **Reliability**: Fewer moving parts, less chance of failure
5. **Prometheus Pattern**: Follows proven pattern from existing codebase

## Monitoring

Monitor these metrics:
- Event publishing success rate
- Event publishing latency
- RabbitMQ queue depth
- Failed event count

## Error Handling

Events are published with try-catch blocks to ensure business logic continues even if event publishing fails:

```python
try:
    self.event_service.publish_credit_created(...)
except Exception as e:
    logger.warning(f"Failed to publish event: {e}")
    # Business logic continues
```

This ensures that event publishing failures don't break the main business flow.

## Next Steps

1. Deploy the simplified framework
2. Monitor event publishing
3. Add more event types as needed
4. Build event consumers for downstream systems
5. Add monitoring and alerting
