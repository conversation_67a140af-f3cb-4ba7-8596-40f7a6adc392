# AR Event Framework - Quick Reference

## Event Types

| Event Type | Description | Event Class |
|------------|-------------|-------------|
| `com.treebo.ar.credit.created` | Credit creation | `CreditCreatedEvent` |
| `com.treebo.ar.credit.cancelled` | Credit cancellation | `CreditCancelledEvent` |
| `com.treebo.ar.credit.reversed` | Credit reversal | `CreditReversalCreatedEvent` |
| `com.treebo.ar.credit.reversal.cancelled` | Credit reversal cancellation | `CreditReversalCancelledEvent` |
| `com.treebo.ar.debit.created` | Debit creation | `DebitCreatedEvent` |
| `com.treebo.ar.debit.updated` | Debit update | `DebitUpdatedEvent` |
| `com.treebo.ar.debit.cancelled` | Debit cancellation | `DebitCancelledEvent` |

## Quick Start

### 1. Emit Credit Created Event

```python
from ar_module.infrastructure.events import emit_credit_created_event
from ar_module.domain.events.base_event import EventSource

emit_credit_created_event(
    credit_id="CR-001",
    recorded_via=EventSource.API,
    credit_data={
        "credit_amount": 1000.0,
        "currency": "INR",
        "debtor_id": "debtor-123"
    }
)
```

### 2. Emit Debit Created Event

```python
from ar_module.infrastructure.events import emit_debit_created_event

emit_debit_created_event(
    debit_id="DB-001",
    recorded_via=EventSource.API,
    debit_data={
        "debit_amount": 5000.0,
        "currency": "INR",
        "debtor_id": "debtor-123"
    }
)
```

### 3. Handle Events

```python
from ar_module.infrastructure.events.event_handler import event_handler

@event_handler(['com.treebo.ar.credit.created'])
def handle_credit_created(event):
    print(f"Credit {event.entity_id} created")
    return True
```

## Event Schema

### Required Fields

- `id`: Unique event identifier
- `type`: Event type (e.g., `com.treebo.ar.credit.created`)
- `source`: Event source (e.g., `ar-module/credit`)
- `entity_type`: AR entity type (`credit`, `debit`, etc.)
- `user_action`: Action performed (`create`, `update`, etc.)
- `recorded_via`: Source method (`api`, `bulk_upload`, etc.)
- `entity_id`: Entity identifier
- `tenant_id`: Tenant identifier

### Optional Fields

- `user_id`: User identifier
- `hotel_id`: Hotel identifier
- `data`: Event-specific payload

## Event Sources (recorded_via)

- `EventSource.API`: API requests
- `EventSource.BULK_UPLOAD`: Bulk upload operations
- `EventSource.SYSTEM`: System-generated events
- `EventSource.INTEGRATION`: External integrations
- `EventSource.MIGRATION`: Data migration
- `EventSource.MANUAL`: Manual operations

## Configuration

### Exchange and Routing

- **Exchange**: `ar-integration-events`
- **Exchange Type**: `topic`
- **Routing Pattern**: `ar.events.{entity_type}.{action}`

### Examples

- Credit created: `ar.events.credit.create`
- Debit cancelled: `ar.events.debit.cancel`
- Credit reversed: `ar.events.credit.reverse`

## Testing

### Unit Tests

```python
from ar_module.infrastructure.events.event_publisher import SynchronousEventPublisher
from ar_module.infrastructure.events.event_service import set_event_service, AREventService

# Setup
sync_publisher = SynchronousEventPublisher()
test_service = AREventService(event_publisher=sync_publisher)
set_event_service(test_service)

# Test
emit_credit_created_event(...)

# Verify
events = sync_publisher.get_published_events()
assert len(events) == 1
```

## Common Patterns

### Service Integration

```python
class CreditService:
    def __init__(self, ..., event_service: AREventService):
        self.event_service = event_service
    
    def create_credit(self, data):
        credit = self._create_credit_logic(data)
        
        # Emit event
        self.event_service.emit_credit_created(
            credit_id=credit.id,
            recorded_via=EventSource.API,
            credit_data=data
        )
        
        return credit
```

### Error Handling

```python
try:
    emit_credit_created_event(...)
except Exception as e:
    logger.warning(f"Failed to emit event: {e}")
    # Continue with business logic
```

### Batch Events

```python
from ar_module.infrastructure.events import publish_events

events = [event1, event2, event3]
success = publish_events(events)
```

## Debugging

### Enable Debug Logging

```python
import logging
logging.getLogger('ar_module.infrastructure.events').setLevel(logging.DEBUG)
```

### Check Event Service

```python
from ar_module.infrastructure.events.event_service import get_event_service

service = get_event_service()
print(f"Enabled: {service.is_enabled()}")
```

### Validate Event

```python
from ar_module.domain.events.schema import validate_event_schema

is_valid, errors = validate_event_schema(event_data)
if not is_valid:
    print(f"Errors: {errors}")
```

## Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `AR_EVENT_EXCHANGE` | RabbitMQ exchange name | `ar-integration-events` |
| `AR_EVENT_DEBUG` | Enable debug logging | `false` |
| `AR_EVENT_VALIDATION` | Enable event validation | `false` |

## Monitoring

### Key Metrics

- Event publishing success rate
- Event processing latency
- Queue depth
- Handler success rate

### Health Checks

```python
# Check publisher health
publisher = get_event_publisher()
# Check if publisher is connected and operational

# Check handler registry
registry = get_event_handler_registry()
handlers = registry.get_handlers_for_event(test_event)
print(f"Registered handlers: {len(handlers)}")
```

## Migration Checklist

- [ ] Add `AREventService` to service dependencies
- [ ] Emit events after successful operations
- [ ] Add error handling for event publishing
- [ ] Write tests for event emission
- [ ] Update service registration with new dependency
- [ ] Configure RabbitMQ exchange and queues
- [ ] Set up monitoring and alerting

## Support

For issues or questions:

1. Check the main documentation: `docs/ar_event_framework.md`
2. Review test examples in `tests/unit/infrastructure/events/`
3. Check existing event implementations in services
4. Contact the AR team for assistance
