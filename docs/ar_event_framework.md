# AR Module Event Framework

## Overview

The AR (Accounts Receivable) Event Framework is a comprehensive event-driven architecture that enables the AR module to publish and consume events for key business operations. The framework follows the CloudEvents specification and provides a robust, scalable solution for integration and audit purposes.

## Key Features

- **CloudEvents Compliant**: Events follow the CloudEvents v1.0 specification
- **AR-Specific Schema**: Extended with AR-specific attributes (entity_type, source, user_action)
- **RabbitMQ Integration**: Built on existing RabbitMQ messaging infrastructure
- **Event Validation**: JSON schema validation for event payloads
- **Flexible Publishing**: Support for both synchronous and asynchronous publishing
- **Event Handling**: Pluggable event handler system
- **Multi-tenant Support**: Tenant-aware event processing

## Architecture

### Core Components

1. **Event Schema**: CloudEvents-compliant schema with AR extensions
2. **Event Publisher**: RabbitMQ-based event publishing
3. **Event Handler**: Pluggable event handling system
4. **Event Service**: High-level service for event management
5. **Configuration**: Environment-specific configurations

### Event Flow

```
Business Operation → Event Creation → Event Publishing → RabbitMQ → Event Consumers
                                  ↓
                              Local Handlers
```

## Event Schema

### Base Event Structure

All AR events follow this structure:

```json
{
  "id": "550e8400-e29b-41d4-a716-446655440000",
  "specversion": "1.0",
  "type": "com.treebo.ar.credit.created",
  "source": "ar-module/credit",
  "time": "2024-01-15T10:30:00Z",
  "subject": "credit/CR-2024-001",
  "datacontenttype": "application/json",
  
  "entity_type": "credit",
  "user_action": "create",
  "recorded_via": "api",
  "entity_id": "CR-2024-001",
  "tenant_id": "tenant-123",
  "user_id": "user-456",
  "hotel_id": "hotel-789",
  
  "data": {
    "credit_amount": 1000.00,
    "currency": "INR",
    "debtor_id": "debtor-123"
  }
}
```

### AR-Specific Attributes

- **entity_type**: Type of AR entity (credit, debit, settlement, etc.)
- **user_action**: Action performed (create, update, cancel, reverse, etc.)
- **recorded_via**: Source of the event (api, bulk_upload, system, etc.)
- **entity_id**: Unique identifier of the entity
- **tenant_id**: Tenant identifier
- **user_id**: User who performed the action (optional)
- **hotel_id**: Hotel identifier (optional)

## Supported Events

### Credit Events

1. **Credit Created** (`com.treebo.ar.credit.created`)
   - Emitted when a new credit is created
   - Includes credit amount, currency, debtor details

2. **Credit Cancelled** (`com.treebo.ar.credit.cancelled`)
   - Emitted when a credit is cancelled
   - Includes cancellation reason and timestamp

3. **Credit Reversed** (`com.treebo.ar.credit.reversed`)
   - Emitted when a credit reversal is created
   - Includes original credit ID and reversal details

4. **Credit Reversal Cancelled** (`com.treebo.ar.credit.reversal.cancelled`)
   - Emitted when a credit reversal is cancelled
   - Includes original credit and reversal details

### Debit Events

1. **Debit Created** (`com.treebo.ar.debit.created`)
   - Emitted when a new debit is created
   - Includes debit amount, due date, invoice details

2. **Debit Updated** (`com.treebo.ar.debit.updated`)
   - Emitted when a debit is updated
   - Includes updated fields and change details

3. **Debit Cancelled** (`com.treebo.ar.debit.cancelled`)
   - Emitted when a debit is cancelled
   - Includes cancellation reason and timestamp

## Usage Examples

### Basic Event Publishing

```python
from ar_module.infrastructure.events import emit_credit_created_event
from ar_module.domain.events.base_event import EventSource

# Emit a credit created event
result = emit_credit_created_event(
    credit_id="CR-2024-001",
    recorded_via=EventSource.API,
    credit_data={
        "credit_amount": 1000.0,
        "currency": "INR",
        "debtor_id": "debtor-123",
        "payment_mode": "cash",
        "credit_date": "2024-01-15",
        "reference_number": "REF-001"
    },
    tenant_id="tenant-123",
    user_id="user-456",
    hotel_id="hotel-789"
)
```

### Using Event Service

```python
from ar_module.infrastructure.events.event_service import get_event_service
from ar_module.domain.events.base_event import EventSource

event_service = get_event_service()

# Emit debit created event
result = event_service.emit_debit_created(
    debit_id="DB-2024-001",
    recorded_via=EventSource.API,
    debit_data={
        "debit_amount": 5000.0,
        "currency": "INR",
        "debtor_id": "debtor-123",
        "debit_date": "2024-01-15",
        "due_date": "2024-02-15",
        "invoice_number": "INV-001"
    }
)
```

### Creating Custom Events

```python
from ar_module.domain.events.base_event import AREvent, EntityType, UserAction, EventSource

# Create a custom event
custom_event = AREvent(
    entity_type=EntityType.SETTLEMENT,
    user_action=UserAction.CREATE,
    recorded_via=EventSource.SYSTEM,
    entity_id="settlement-123",
    tenant_id="tenant-456",
    data={"settlement_amount": 2000.0}
)

# Publish the event
from ar_module.infrastructure.events import publish_event
result = publish_event(custom_event)
```

### Event Handling

```python
from ar_module.infrastructure.events.event_handler import event_handler
from ar_module.domain.events.base_event import BaseEvent

# Register an event handler using decorator
@event_handler(['com.treebo.ar.credit.created'])
def handle_credit_created(event: BaseEvent) -> bool:
    print(f"Credit created: {event.entity_id}")
    # Process the event
    return True

# Register a handler class
from ar_module.infrastructure.events.event_handler import TypedEventHandler, register_event_handler

class CreditAuditHandler(TypedEventHandler):
    def __init__(self):
        super().__init__(['com.treebo.ar.credit.created', 'com.treebo.ar.credit.cancelled'])
    
    def handle(self, event: BaseEvent) -> bool:
        # Audit the credit event
        print(f"Auditing credit event: {event.type}")
        return True

# Register the handler
register_event_handler(CreditAuditHandler())
```

## Configuration

### Environment Configuration

```python
from ar_module.infrastructure.events.event_config import get_event_config_for_environment

# Get configuration for different environments
dev_config = get_event_config_for_environment("development")
prod_config = get_event_config_for_environment("production")

# Check configuration settings
print(f"Exchange: {dev_config.get_exchange_name()}")
print(f"Debug logging: {dev_config.is_debug_logging_enabled()}")
```

### Publisher Configuration

```python
from ar_module.infrastructure.events.event_config import get_publisher_config

# Get publisher configuration for a tenant
config = get_publisher_config("tenant-123")
print(f"Exchange: {config.exchange_name}")
print(f"Routing keys: {config.routing_keys}")
```

## Testing

### Unit Testing with Synchronous Publisher

```python
from ar_module.infrastructure.events.event_publisher import SynchronousEventPublisher
from ar_module.infrastructure.events.event_service import set_event_service, AREventService

# Set up synchronous publisher for testing
sync_publisher = SynchronousEventPublisher()
test_service = AREventService(event_publisher=sync_publisher)
set_event_service(test_service)

# Emit events in tests
emit_credit_created_event(
    credit_id="test-credit",
    recorded_via=EventSource.API,
    credit_data={"amount": 1000}
)

# Verify events were published
published_events = sync_publisher.get_published_events()
assert len(published_events) == 1
assert published_events[0].entity_id == "test-credit"
```

### Integration Testing

```python
from ar_module.infrastructure.events.event_handler import EventHandlerRegistry

# Set up test event handler
test_registry = EventHandlerRegistry()
handled_events = []

def test_handler(event):
    handled_events.append(event)
    return True

test_registry.register_function_handler(test_handler, ['com.treebo.ar.credit.created'])

# Create service with test components
test_service = AREventService(
    event_publisher=sync_publisher,
    event_handler_registry=test_registry
)

# Test end-to-end flow
test_service.emit_credit_created(
    credit_id="test-credit",
    recorded_via=EventSource.API,
    credit_data={"amount": 1000}
)

# Verify both publishing and handling
assert len(sync_publisher.get_published_events()) == 1
assert len(handled_events) == 1
```

## Best Practices

1. **Event Naming**: Use descriptive, consistent event type names
2. **Data Payload**: Include only necessary data in event payload
3. **Error Handling**: Always handle event publishing failures gracefully
4. **Testing**: Use synchronous publisher for unit tests
5. **Monitoring**: Monitor event publishing success rates
6. **Schema Evolution**: Plan for backward-compatible schema changes

## Troubleshooting

### Common Issues

1. **Events not being published**
   - Check RabbitMQ connection
   - Verify exchange and queue configuration
   - Check event validation

2. **Events not being handled**
   - Verify event handlers are registered
   - Check routing key patterns
   - Ensure handlers return True for success

3. **Performance issues**
   - Monitor event publishing latency
   - Consider batch publishing for high volume
   - Optimize event payload size

### Debugging

```python
# Enable debug logging
import logging
logging.getLogger('ar_module.infrastructure.events').setLevel(logging.DEBUG)

# Check event service status
from ar_module.infrastructure.events.event_service import get_event_service
service = get_event_service()
print(f"Event service enabled: {service.is_enabled()}")

# Validate event schema
from ar_module.domain.events.schema import validate_event_schema
event_data = {...}  # Your event data
is_valid, errors = validate_event_schema(event_data)
if not is_valid:
    print(f"Validation errors: {errors}")
```

## Migration Guide

### Integrating with Existing Code

1. Add event service dependency to your service classes
2. Emit events after successful business operations
3. Handle event publishing failures appropriately
4. Add tests for event emission

### Example Integration

```python
# In your service class
from ar_module.infrastructure.events.event_service import AREventService

class YourService:
    def __init__(self, ..., event_service: AREventService):
        self.event_service = event_service
    
    def create_credit(self, credit_data):
        # Existing business logic
        credit = self.create_credit_logic(credit_data)
        
        # Emit event
        try:
            self.event_service.emit_credit_created(
                credit_id=credit.id,
                recorded_via=EventSource.API,
                credit_data=credit_data
            )
        except Exception as e:
            logger.warning(f"Failed to emit credit created event: {e}")
        
        return credit
```
